{"name": "senprints-storefront", "version": "4.0.0", "packageManager": "yarn@4.3.1", "author": "senprints", "scripts": {"build": "nuxt build --dotenv .env", "dev": "nuxt dev --host", "generate": "nuxt generate", "preview": "nuxt preview", "start": "node --expose-gc .output/server/index.mjs", "serve": "serve dist/", "postinstall": "husky install", "lint": "eslint", "lint:fix": "eslint --fix", "lintfix": "eslint --fix -c ./eslint.config.mjs", "prepare": "husky install", "clean": "rm -rf .nuxt dist .output", "generate:locales": "node tools/translator.js ./locales en.yml"}, "dependencies": {"@fingerprintjs/fingerprintjs": "3.4.0", "@morev/vue-transitions": "3.0.2", "@nuxtjs/color-mode": "3.4.2", "@pinia/nuxt": "0.5.1", "@sentry/vue": "^7.64.0", "@splidejs/splide-extension-grid": "^0.4.1", "@splidejs/vue-splide": "^0.6.12", "@videojs-player/vue": "^1.0.0", "devtools-detector": "^2.0.22", "dompurify": "^3.1.6", "eslint-plugin-format": "^1.0.1", "is-https": "^4.0.0", "nuxt-viewport": "2.1.5", "nuxt-windicss": "3.0.1", "pinia": "^2.1.7", "pinia-shared-state": "^0.5.1", "qs": "^6.12.3", "three": "^0.126.1", "ua-parser-js": "^1.0.34", "uuid": "^9.0.0", "video.js": "8.17.4", "vue-demi": "^0.14.8", "vue-easy-lightbox": "^1.19.0", "vue-slider-component": "4.1.0-beta.7", "vue-tel-input": "6.0.5", "winston": "^3.13.1", "winston-discord-transport": "^1.3.0", "winston-graylog2": "^2.1.2"}, "devDependencies": {"@antfu/eslint-config": "^4.13.0", "@commitlint/cli": "19.3.0", "@commitlint/config-conventional": "19.2.2", "@nuxt/eslint": "^0.3.13", "@nuxt/test-utils-edge": "3.8.0-28284309.b3d3d7f4", "@nuxtjs/i18n": "8.3.1", "@pinia-plugin-persistedstate/nuxt": "1.2.1", "@types/dompurify": "^3.0.5", "@types/sanitize-html": "^2.11.0", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^9.0.1", "@types/vue-tel-input": "2.1.2", "@typescript-eslint/eslint-plugin": "^7.16.1", "consola": "^3.2.3", "eslint": "^9.26.0", "husky": "9.0.11", "lint-staged": "15.2.7", "nuxt": "^3.12.3", "nuxt-schema-org": "^3.3.9", "postcss": "8.4.39", "prettier": "3.3.3", "sass": "1.77.8", "translate": "3.0.0", "typescript": "^5.5.3"}, "lint-staged": {"**/*.{js,ts,vue}": ["yarn lintfix"]}}