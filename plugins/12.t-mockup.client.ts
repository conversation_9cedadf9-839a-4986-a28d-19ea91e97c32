import * as THREE from 'three'
import { Vector2 } from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'

// Global Three.js reference for compatibility
if (typeof window !== 'undefined') {
  (window as any).THREE = THREE
}

// Get fabric from global window (loaded via design-canvas.js)
function getFabric() {
  if (typeof window !== 'undefined') {
    return (window as any).fabric || (window as any).DesignCanvas?.fabric
  }
  return null
}

let globalRenderer: THREE.WebGLRenderer | undefined

export class TMockupRender {
  mockup: any
  scene: THREE.Scene
  camera: THREE.PerspectiveCamera
  ocamera: THREE.OrthographicCamera | THREE.PerspectiveCamera
  renderer: THREE.WebGLRenderer
  print: any
  material: THREE.MeshBasicMaterial | null = null
  container: HTMLElement = document.createElement('div')
  locked: boolean = false
  exported: boolean = false
  hasDesign: boolean = false
  mockupType: string = 'default'
  bgTexture: THREE.Texture | null = null
  bgLayer: any = null
  canvasEl: HTMLCanvasElement | null = null

  // Additional properties from Vue 2 version
  canvas: any = null
  width: number = 0
  height: number = 0
  designCanvas: HTMLCanvasElement | null = null
  shortcut: any = null
  wireframe: THREE.LineSegments | null = null
  design: any = null
  designGroup: any = null

  constructor(mockup: any) {
    if (!mockup || !mockup.background) {
      return
    }

    this.exported = false
    this.hasDesign = false
    this.mockup = mockup
    this.mockupType = mockup.mockupType || 'default'

    if (mockup.glb) {
      const threeContainer = document.getElementById('three')

      if (typeof globalRenderer === 'undefined') {
        globalRenderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true,
          preserveDrawingBuffer: true
        })
      }

      this.renderer = globalRenderer
      this.renderer.setClearColor(0xCCCCCC, 0)
      if (threeContainer) {
        threeContainer.appendChild(this.renderer.domElement)
      }

      const fov = 75
      const aspect = window.innerWidth / window.innerHeight
      const near = 0.1
      const far = 25000
      this.camera = new THREE.PerspectiveCamera(fov, aspect, near, far)
      this.camera.position.z = 1000
      this.camera.lookAt(0, 0, 0)

      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xCCCCCC)
      this.scene.add(new THREE.AmbientLight(0xFFFFFF, 1.0))
    }

    // Set container and canvas
    this.container = mockup.canvasEl?.parentElement || document.createElement('div')
    this.canvasEl = mockup.canvasEl || null
  }

  async render(): Promise<boolean> {
    if (!this.mockup.glb) {
      return false
    }

    return this._loadObj(600, false)
  }

  _loadObj(width: number = 600, background: boolean = false): boolean {
    const mockup = this.mockup

    if (!mockup || !mockup.glb) {
      return false
    }

    const camera = this.camera
    const scene = this.scene
    const renderer = this.renderer

    // Load background texture
    const texture = this.bgTexture = mockup.texture
    if (texture && texture.image) {
      const aspect = texture.image.width / texture.image.height
      const height = width / aspect

      renderer.setSize(width, height)
      camera.aspect = width / height
      camera.updateProjectionMatrix()

      scene.background = background ? texture : null
    }

    // Load print layer from GLTF scene
    const printScene = mockup.glb.scene
    const parent = this

    printScene.traverse((child: any) => {
      if (child.isMesh) {
        if (child.name === 'background') {
          parent.bgLayer = child
          parent._initOgCamera(child)
          parent._fitCameraToBgImage(child)
        }
        else if (child.name === mockup.name) {
          parent.print = child
        }
      }
    })

    // Remove background layer
    if (this.bgLayer) {
      printScene.remove(this.bgLayer)
    }

    scene.add(printScene)

    // Apply design if available
    if (this.print && mockup.designImg) {
      const designCanvas = this._correctDesignSize(mockup.designImg, mockup.printWidth, mockup.printHeight)
      const designTexture = new THREE.CanvasTexture(designCanvas)
      designTexture.flipY = false
      const material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
      this.print.material = this.material = material
      this.print.material.needsUpdate = true

      this.render3D()
      return this._glbCanvas() !== null
    }

    return false
  }

  render3D(): void {
    if (this.renderer && this.ocamera) {
      this.renderer.render(this.scene, this.ocamera)
    }
  }

  _initOgCamera(object: any): void {
    const geometry = object.geometry
    const size = geometry.boundingBox.getSize(new THREE.Vector3())

    const camera = new THREE.OrthographicCamera(
      size.x / -2,
      size.x / 2,
      size.y / 2,
      size.y / -2,
      0.1,
      2000
    )
    camera.position.z = 1000
    this.ocamera = camera
  }

  _fitCameraToBgImage(bgLayer: any): void {
    // Fit camera to background image dimensions
    if (bgLayer && bgLayer.geometry) {
      const box = new THREE.Box3().setFromObject(bgLayer)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())

      this.ocamera.position.copy(center)
      this.ocamera.position.z += Math.max(size.x, size.y) * 2
      this.ocamera.lookAt(center)
    }
  }

  hide(): void {
    if (this.canvasEl && this.canvasEl.parentElement) {
      this.canvasEl.parentElement.style.display = 'none'
    }
  }

  show(): void {
    if (this.canvasEl && this.canvasEl.parentElement) {
      this.canvasEl.parentElement.style.display = 'block'
    }
  }

  setColor(color: string): void {
    // Implementation for color changes
    if (this.material) {
      this.material.color.setHex(Number.parseInt(color.replace('#', '0x')))
      this.material.needsUpdate = true
      this.render3D()
    }
  }

  updateDesign(designImg: string): void {
    if (this.print && designImg) {
      const designCanvas = this._correctDesignSize(designImg, this.mockup.printWidth, this.mockup.printHeight)
      const designTexture = new THREE.CanvasTexture(designCanvas)
      designTexture.flipY = false

      if (this.material) {
        this.material.map = designTexture
        this.material.needsUpdate = true
      }
      else {
        this.material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
        this.print.material = this.material
      }

      this.render3D()
    }
  }

  exportCanvas(): HTMLCanvasElement | null {
    return this.renderer?.domElement || null
  }

  // Additional methods from Vue 2 version
  changeDesign(png: string | null = null): Promise<TMockupRender> {
    const mockup = this.mockup
    return new Promise((resolve, reject) => {
      const parent = this
      const print = this.print
      parent.hasDesign = true

      // Load custom design
      if (!png) {
        png = `./png/${Math.floor(Math.random() * 5) + 1}.png`
      }

      parent._loadImg(png).then((img: any) => {
        const designCanvas = parent._correctDesignSize(img, mockup.printWidth, mockup.printHeight)
        const designTexture = new THREE.CanvasTexture(designCanvas)
        designTexture.flipY = false

        if (!parent.material) {
          parent.print.material = parent.material = new THREE.MeshBasicMaterial({
            transparent: true,
            map: designTexture
          })
        }
        else {
          print.material.map = designTexture
        }

        print.material.needsUpdate = true
        parent.render3D()

        const fabric = getFabric()
        if (fabric) {
          const newDesign = new fabric.Image(parent._glbCanvas())
          parent._changeCanvasDesign(newDesign)
        }

        resolve(parent)
      }).catch(reject)
    })
  }

  updateDesignCanvas(canvas: HTMLCanvasElement, hasDesign: boolean = true): void {
    try {
      if (!this.print) { return }

      this.designCanvas = canvas
      const designTexture = new THREE.CanvasTexture(canvas)
      designTexture.flipY = false

      if (!this.material) {
        this.material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
        this.print.material = this.material
      }
      else {
        this.print.material.map = designTexture
      }

      this.print.material.needsUpdate = true
      this.render3D()

      const fabric = getFabric()
      if (fabric) {
        const newDesign = new fabric.Image(this._glbCanvas())
        this._changeCanvasDesign(newDesign)
      }

      if (this.shortcut) {
        this.shortcut.renderAll()
      }
      this.hasDesign = hasDesign
      this.exported = false
    }
    catch (e) {
      console.error('Error updating design canvas:', e)
    }
  }

  _loadImg(fileUrl: string): Promise<any> {
    const fabric = getFabric()
    if (!fileUrl || !fabric) {
      return Promise.reject(new Error('Fabric not available'))
    }
    return new Promise((resolve, reject) => {
      fabric.Image.fromURL(fileUrl, (img: any) => {
        resolve(img)
      }, { crossOrigin: 'Anonymous' })
    })
  }

  _changeCanvasDesign(newDesign: any): void {
    const fabric = getFabric()
    if (!this.canvas || !fabric)
      return

    if (newDesign) {
      newDesign.scaleToWidth(this.width)
      newDesign.set({
        top: -this.designGroup.height / 2,
        left: -this.designGroup.width / 2
      })
    }
    else {
      newDesign = new fabric.Rect({
        width: 1,
        height: 1,
        opacity: 0
      })
    }

    if (this.mockup.cropImg) {
      newDesign.set({ globalCompositeOperation: 'source-in' })
    }

    this.designGroup.remove(this.design)
    this.designGroup.add(newDesign)
    this.design = newDesign

    this.canvas.renderAll()
  }

  // Additional utility methods
  designPNG(): string {
    this.exported = true
    this.render3D()
    return this.renderer.domElement.toDataURL('image/png')
  }

  designBlob(callback: (blob: Blob | null) => void, printSize: number = 1500): void {
    if (!this.hasDesign) {
      return callback(null)
    }
    const originSize = this.renderer.getSize(new Vector2())
    const width = printSize
    const height = printSize * 1.25
    this.renderer.setSize(width, height)
    this.render3D()
    this.renderer.domElement.toBlob(callback, 'image/png', 1.0)
    this.renderer.setSize(originSize.width, originSize.height)
  }

  addShortcutCanvas(containerID: string, width?: number): any {
    const fabric = getFabric()
    if (this.shortcut || !fabric) {
      return this.shortcut
    }

    const container = document.getElementById(containerID)
    if (!container)
      return null

    const divEl = document.createElement('div')
    divEl.style.float = 'left'
    const canvasEl = document.createElement('canvas')
    divEl.appendChild(canvasEl)
    container.appendChild(divEl)
    const canvas = new fabric.Canvas(canvasEl)

    if (!width) { width = this.width }
    const height = this.height * width / this.width

    canvas.setWidth(width)
    canvas.setHeight(height)

    const img = new fabric.Image(this.canvasEl)
    img.scaleToWidth(width)
    canvas.add(img)

    // disable selection
    canvas.selection = false
    canvas.forEachObject((o: any) => {
      o.selectable = false
      o.hoverCursor = 'default'
    })

    canvas.renderAll()
    this.shortcut = canvas
    return canvas
  }

  viewDesign(): void {
    const w = window.open('', '')
    if (w) {
      w.document.title = 'Screenshot'
      const img = new Image()
      img.onload = function () {
        w.document.body.appendChild(img)
      }
      img.src = this.designPNG()
    }
  }

  showWireframe(show: boolean): void {
    const print = this.print
    if (!print) { return }

    if (show) {
      if (!this.wireframe) {
        // Setup wireframe
        const wireframeGeometry = new THREE.WireframeGeometry(print.geometry)
        const wireframeMaterial = new THREE.LineBasicMaterial({ color: 0xFF0000 })
        this.wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial)
      }
      print.add(this.wireframe)
    }
    else {
      if (this.wireframe) {
        print.remove(this.wireframe)
      }
    }
    this.render3D()
  }

  showBgImage(show: boolean): void {
    const scene = this.scene
    if (show) {
      scene.background = this.bgTexture
    }
    else {
      scene.background = null
    }
    this.render3D()
  }

  _glbCanvas(): HTMLCanvasElement {
    return this.renderer.domElement
  }

  _correctDesignSize(designImg: any, printWidth: number, printHeight: number): HTMLCanvasElement {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Could not get canvas context')
    }

    canvas.width = printWidth
    canvas.height = printHeight

    // Calculate scaling to fit the design image within the print area
    const scaleX = printWidth / designImg.width
    const scaleY = printHeight / designImg.height
    const scale = Math.min(scaleX, scaleY)

    const scaledWidth = designImg.width * scale
    const scaledHeight = designImg.height * scale

    // Center the image
    const x = (printWidth - scaledWidth) / 2
    const y = (printHeight - scaledHeight) / 2

    ctx.drawImage(designImg.getElement ? designImg.getElement() : designImg, x, y, scaledWidth, scaledHeight)

    return canvas
  }
}

export class TMockupLoader {
  gltfLoader: GLTFLoader
  gltf: any = null

  constructor() {
    this.gltfLoader = new GLTFLoader()
    // Set CORS for loading external models
    if (typeof THREE.ImageUtils !== 'undefined') {
      THREE.ImageUtils.crossOrigin = ''
    }
  }

  async loadMockup(params: TMockupParams): Promise<any> {
    const mockup: any = {
      id: params.id,
      name: params.name || 'default',
      maxSize: params.maxSize || 600,
      background: params.background,
      designImg: params.designImg,
      color: params.color || '#ffffff',
      mockupType: params.mockupType || 'flat',
      printWidth: params.printWidth || 2000,
      printHeight: params.printHeight || 2500,
      canvasEl: params.canvasEl
    }

    // Load background texture
    if (params.background) {
      mockup.texture = await this._loadTexture(params.background)
    }

    // Load GLTF model
    if (params.glb) {
      mockup.glb = await this._loadObj(params.glb)
      mockup.name = params.name || 'print' // Default print object name
    }

    // Load additional textures
    if (params.colorImg) {
      mockup.colorTexture = await this._loadTexture(params.colorImg)
    }

    if (params.cropImg) {
      mockup.cropTexture = await this._loadTexture(params.cropImg)
    }

    if (params.shadowImg) {
      mockup.shadowTexture = await this._loadTexture(params.shadowImg)
    }

    return mockup
  }

  private async _loadObj(fileUrl: string): Promise<any> {
    if (!fileUrl) {
      throw new Error('No GLTF file URL provided')
    }

    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        fileUrl,
        (gltf: any) => {
          this.gltf = gltf
          resolve(gltf)
        },
        (progress: any) => {
          // Loading progress callback
          console.log('Loading progress:', progress)
        },
        (error: any) => {
          console.error('Error loading GLTF:', error)
          reject(error)
        }
      )
    })
  }

  private async _loadTexture(fileUrl: string): Promise<THREE.Texture> {
    if (!fileUrl) {
      throw new Error('No texture file URL provided')
    }

    return new Promise((resolve, reject) => {
      const textureLoader = new THREE.TextureLoader()
      textureLoader.load(
        fileUrl,
        (texture: THREE.Texture) => {
          resolve(texture)
        },
        (progress: any) => {
          // Loading progress callback
          console.log('Texture loading progress:', progress)
        },
        (error: any) => {
          console.error('Error loading texture:', error)
          reject(error)
        }
      )
    })
  }
}

class MockupManager {
  container: HTMLElement
  mockupLoader: TMockupLoader
  mockups: { [key: string]: TMockupRender } = {}
  activeMockup: TMockupRender | null = null
  activeMockupId: string | null = null
  locked: boolean = false

  constructor(containerID?: string) {
    this.container = containerID
      ? document.getElementById(containerID) || document.createElement('div')
      : document.createElement('div')
    this.mockupLoader = new TMockupLoader()
    this.mockups = {}
  }

  async loadMockup(params: TMockupParams): Promise<TMockupRender> {
    const id = String(params.id)

    if (this.mockups[id]) {
      return this.mockups[id]
    }

    // Create canvas element for this mockup
    const canvasEl = document.createElement('canvas')
    canvasEl.id = `design_${id}`
    this.container.appendChild(canvasEl)
    params.canvasEl = canvasEl

    // Load mockup data
    const mockup = await this.mockupLoader.loadMockup(params)

    // Create mockup render
    const mockupRender = new TMockupRender(mockup)
    await mockupRender.render()
    this.mockups[id] = mockupRender

    // Set as active if first mockup
    if (!this.activeMockup) {
      this.activeMockup = mockupRender
      this.activeMockupId = id
    }
    else {
      mockupRender.hide()
    }

    return mockupRender
  }

  getMockup(id: string): TMockupRender | false {
    return this.mockups[id] || false
  }

  getActiveMockup(): TMockupRender | null {
    return this.activeMockup
  }

  setActiveMockup(id: string): TMockupRender | false {
    if (!id) {
      id = this.activeMockupId || ''
    }

    if (!id || !this.mockups[id]) {
      return false
    }

    if (this.activeMockup !== this.mockups[id]) {
      if (this.activeMockup) {
        this.activeMockup.hide()
      }
      this.activeMockup = this.mockups[id]
      this.activeMockup.show()
    }
    else {
      this.activeMockup.show()
    }

    this.activeMockupId = id
    return this.activeMockup
  }

  hideActiveMockup(): void {
    if (this.activeMockup) {
      this.activeMockup.hide()
    }
  }

  toDataURL(format: string = 'png'): string | null {
    if (this.activeMockup && this.activeMockup.exportCanvas()) {
      return this.activeMockup.exportCanvas()?.toDataURL(`image/${format}`) || null
    }
    return null
  }

  getElement(): HTMLCanvasElement | null {
    if (this.activeMockup && this.activeMockup.renderer) {
      return this.activeMockup.renderer.domElement
    }
    return null
  }
}

export default defineNuxtPlugin(() => {
  return {
    provide: {
      MockupManager,
      TMockupRender,
      TMockupLoader
    }
  }
})
