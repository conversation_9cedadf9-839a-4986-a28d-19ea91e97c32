import * as THREE from 'three'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader'

// Global Three.js reference for compatibility
if (typeof window !== 'undefined') {
  (window as any).THREE = THREE
}

let globalRenderer: THREE.WebGLRenderer | undefined

export class TMockupRender {
  mockup: any
  scene: THREE.Scene
  camera: THREE.PerspectiveCamera
  ocamera: THREE.PerspectiveCamera
  renderer: THREE.WebGLRenderer
  print: any
  material: THREE.MeshBasicMaterial | null = null
  container: HTMLElement
  locked: boolean = false
  exported: boolean = false
  hasDesign: boolean = false
  mockupType: string
  bgTexture: THREE.Texture | null = null
  bgLayer: any = null
  canvasEl: HTMLCanvasElement | null = null

  constructor(mockup: any) {
    if (!mockup || !mockup.background) {
      throw new Error('Invalid mockup data')
    }

    this.exported = false
    this.hasDesign = false
    this.mockup = mockup
    this.mockupType = mockup.mockupType || 'default'

    if (mockup.glb) {
      // Initialize Three.js renderer
      if (typeof globalRenderer === 'undefined') {
        globalRenderer = new THREE.WebGLRenderer({
          antialias: true,
          alpha: true,
          preserveDrawingBuffer: true
        })
      }

      this.renderer = globalRenderer
      this.renderer.setClearColor(0xCCCCCC, 0)

      // Initialize camera
      const fov = 75
      const aspect = window.innerWidth / window.innerHeight
      const near = 0.1
      const far = 25000
      this.camera = new THREE.PerspectiveCamera(fov, aspect, near, far)
      this.camera.position.z = 1000
      this.camera.lookAt(0, 0, 0)

      // Initialize scene
      this.scene = new THREE.Scene()
      this.scene.background = new THREE.Color(0xCCCCCC)
      this.scene.add(new THREE.AmbientLight(0xFFFFFF, 1.0))

      // Set container
      this.container = mockup.canvasEl?.parentElement || document.createElement('div')
      this.canvasEl = mockup.canvasEl || null
    }
  }

  async render(): Promise<boolean> {
    if (!this.mockup.glb) {
      return false
    }

    return this._loadObj(600, false)
  }

  _loadObj(width: number = 600, background: boolean = false): boolean {
    const mockup = this.mockup

    if (!mockup || !mockup.glb) {
      return false
    }

    const camera = this.camera
    const scene = this.scene
    const renderer = this.renderer

    // Load background texture
    const texture = this.bgTexture = mockup.texture
    if (texture && texture.image) {
      const aspect = texture.image.width / texture.image.height
      const height = width / aspect

      renderer.setSize(width, height)
      camera.aspect = width / height
      camera.updateProjectionMatrix()

      scene.background = background ? texture : null
    }

    // Load print layer from GLTF scene
    const printScene = mockup.glb.scene
    const parent = this

    printScene.traverse((child: any) => {
      if (child.isMesh) {
        if (child.name === 'background') {
          parent.bgLayer = child
          parent._initOgCamera(child)
          parent._fitCameraToBgImage(child)
        }
        else if (child.name === mockup.name) {
          parent.print = child
        }
      }
    })

    // Remove background layer
    if (this.bgLayer) {
      printScene.remove(this.bgLayer)
    }

    scene.add(printScene)

    // Apply design if available
    if (this.print && mockup.designImg) {
      const designCanvas = this._correctDesignSize(mockup.designImg, mockup.printWidth, mockup.printHeight)
      const designTexture = new THREE.CanvasTexture(designCanvas)
      designTexture.flipY = false
      const material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
      this.print.material = this.material = material
      this.print.material.needsUpdate = true

      this.render3D()
      return this._glbCanvas() !== null
    }

    return false
  }

  render3D(): void {
    if (this.renderer && this.ocamera) {
      this.renderer.render(this.scene, this.ocamera)
    }
  }

  _initOgCamera(bgLayer: any): void {
    // Initialize original camera based on background layer
    this.ocamera = this.camera.clone()
  }

  _fitCameraToBgImage(bgLayer: any): void {
    // Fit camera to background image dimensions
    if (bgLayer && bgLayer.geometry) {
      const box = new THREE.Box3().setFromObject(bgLayer)
      const center = box.getCenter(new THREE.Vector3())
      const size = box.getSize(new THREE.Vector3())

      this.ocamera.position.copy(center)
      this.ocamera.position.z += Math.max(size.x, size.y) * 2
      this.ocamera.lookAt(center)
    }
  }

  _correctDesignSize(designImg: any, printWidth: number, printHeight: number): HTMLCanvasElement {
    // Create canvas with correct design size
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    canvas.width = printWidth || 2000
    canvas.height = printHeight || 2500

    if (designImg instanceof HTMLImageElement) {
      ctx.drawImage(designImg, 0, 0, canvas.width, canvas.height)
    }
    else if (typeof designImg === 'string') {
      // Handle base64 or URL strings
      const img = new Image()
      img.onload = () => {
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      }
      img.src = designImg
    }

    return canvas
  }

  _glbCanvas(): HTMLCanvasElement | null {
    return this.renderer?.domElement || null
  }

  hide(): void {
    if (this.canvasEl && this.canvasEl.parentElement) {
      this.canvasEl.parentElement.style.display = 'none'
    }
  }

  show(): void {
    if (this.canvasEl && this.canvasEl.parentElement) {
      this.canvasEl.parentElement.style.display = 'block'
    }
  }

  setColor(color: string): void {
    // Implementation for color changes
    if (this.material) {
      this.material.color.setHex(Number.parseInt(color.replace('#', '0x')))
      this.material.needsUpdate = true
      this.render3D()
    }
  }

  updateDesign(designImg: string): void {
    if (this.print && designImg) {
      const designCanvas = this._correctDesignSize(designImg, this.mockup.printWidth, this.mockup.printHeight)
      const designTexture = new THREE.CanvasTexture(designCanvas)
      designTexture.flipY = false

      if (this.material) {
        this.material.map = designTexture
        this.material.needsUpdate = true
      }
      else {
        this.material = new THREE.MeshBasicMaterial({ transparent: true, map: designTexture })
        this.print.material = this.material
      }

      this.render3D()
    }
  }

  exportCanvas(): HTMLCanvasElement | null {
    return this.renderer?.domElement || null
  }
}

export class TMockupLoader {
  gltfLoader: GLTFLoader
  gltf: any = null

  constructor() {
    this.gltfLoader = new GLTFLoader()
    // Set CORS for loading external models
    if (typeof THREE.ImageUtils !== 'undefined') {
      THREE.ImageUtils.crossOrigin = ''
    }
  }

  async loadMockup(params: TMockupParams): Promise<any> {
    const mockup: any = {
      id: params.id,
      name: params.name || 'default',
      maxSize: params.maxSize || 600,
      background: params.background,
      designImg: params.designImg,
      color: params.color || '#ffffff',
      mockupType: params.mockupType || 'flat',
      printWidth: params.printWidth || 2000,
      printHeight: params.printHeight || 2500,
      canvasEl: params.canvasEl
    }

    // Load background texture
    if (params.background) {
      mockup.texture = await this._loadTexture(params.background)
    }

    // Load GLTF model
    if (params.glb) {
      mockup.glb = await this._loadObj(params.glb)
      mockup.name = params.name || 'print' // Default print object name
    }

    // Load additional textures
    if (params.colorImg) {
      mockup.colorTexture = await this._loadTexture(params.colorImg)
    }

    if (params.cropImg) {
      mockup.cropTexture = await this._loadTexture(params.cropImg)
    }

    if (params.shadowImg) {
      mockup.shadowTexture = await this._loadTexture(params.shadowImg)
    }

    return mockup
  }

  private async _loadObj(fileUrl: string): Promise<any> {
    if (!fileUrl) {
      throw new Error('No GLTF file URL provided')
    }

    return new Promise((resolve, reject) => {
      this.gltfLoader.load(
        fileUrl,
        (gltf) => {
          this.gltf = gltf
          resolve(gltf)
        },
        (progress) => {
          // Loading progress callback
          console.log('Loading progress:', progress)
        },
        (error) => {
          console.error('Error loading GLTF:', error)
          reject(error)
        }
      )
    })
  }

  private async _loadTexture(fileUrl: string): Promise<THREE.Texture> {
    if (!fileUrl) {
      throw new Error('No texture file URL provided')
    }

    return new Promise((resolve, reject) => {
      const textureLoader = new THREE.TextureLoader()
      textureLoader.load(
        fileUrl,
        (texture) => {
          resolve(texture)
        },
        (progress) => {
          // Loading progress callback
          console.log('Texture loading progress:', progress)
        },
        (error) => {
          console.error('Error loading texture:', error)
          reject(error)
        }
      )
    })
  }
}

class MockupManager {
  container: HTMLElement
  mockupLoader: TMockupLoader
  mockups: { [key: string]: TMockupRender } = {}
  activeMockup: TMockupRender | null = null
  activeMockupId: string | null = null
  locked: boolean = false

  constructor(containerID?: string) {
    this.container = containerID
      ? document.getElementById(containerID) || document.createElement('div')
      : document.createElement('div')
    this.mockupLoader = new TMockupLoader()
    this.mockups = {}
  }

  async loadMockup(params: TMockupParams): Promise<TMockupRender> {
    const id = String(params.id)

    if (this.mockups[id]) {
      return this.mockups[id]
    }

    // Create canvas element for this mockup
    const canvasEl = document.createElement('canvas')
    canvasEl.id = `design_${id}`
    this.container.appendChild(canvasEl)
    params.canvasEl = canvasEl

    // Load mockup data
    const mockup = await this.mockupLoader.loadMockup(params)

    // Create mockup render
    const mockupRender = new TMockupRender(mockup)
    await mockupRender.render()
    this.mockups[id] = mockupRender

    // Set as active if first mockup
    if (!this.activeMockup) {
      this.activeMockup = mockupRender
      this.activeMockupId = id
    }
    else {
      mockupRender.hide()
    }

    return mockupRender
  }

  getMockup(id: string): TMockupRender | false {
    return this.mockups[id] || false
  }

  getActiveMockup(): TMockupRender | null {
    return this.activeMockup
  }

  setActiveMockup(id: string): TMockupRender | false {
    if (!id) {
      id = this.activeMockupId || ''
    }

    if (!id || !this.mockups[id]) {
      return false
    }

    if (this.activeMockup !== this.mockups[id]) {
      if (this.activeMockup) {
        this.activeMockup.hide()
      }
      this.activeMockup = this.mockups[id]
      this.activeMockup.show()
    }
    else {
      this.activeMockup.show()
    }

    this.activeMockupId = id
    return this.activeMockup
  }

  hideActiveMockup(): void {
    if (this.activeMockup) {
      this.activeMockup.hide()
    }
  }

  toDataURL(format: string = 'png'): string | null {
    if (this.activeMockup && this.activeMockup.exportCanvas()) {
      return this.activeMockup.exportCanvas()?.toDataURL(`image/${format}`) || null
    }
    return null
  }

  getElement(): HTMLCanvasElement | null {
    if (this.activeMockup && this.activeMockup.renderer) {
      return this.activeMockup.renderer.domElement
    }
    return null
  }
}

export default defineNuxtPlugin(() => {
  return {
    provide: {
      MockupManager,
      TMockupRender,
      TMockupLoader
    }
  }
})
