<script lang="ts" setup>
import { Splide, SplideSlide } from '@splidejs/vue-splide'
import CsVideoPlayer from '~/components/default/campaign/cs-video-player.vue'
import VideoPlayTrigger from '~/components/default/campaign/video-play-trigger.vue'
import useCampaignImage from '~/composables/campaignImage'
import useCampaignVideo from '~/composables/campaignVideo'

const props = defineProps({
  imagesList: {
    type: Object as PropType<Array<ImageData>>,
    required: true
  },
  campaignData: {
    type: Object as PropType<Campaign>,
    required: true
  },
  currentProduct: {
    type: Object as PropType<Product>,
    required: true
  },
  color: {
    default: undefined,
    type: String
  },
  isModal: {
    default: false,
    type: Boolean
  },
  personalize: {
    default: undefined,
    type: Object as PropType<Personalize>
  },
  isShowDesign: {
    default: false,
    type: Boolean
  }
})
const $emit = defineEmits(['changeImage'])

const { currentImage, initState, updateCurrentImage, colorsMap } = useViewBoxImage()
updateCurrentImage(0)
const viewZone = ref<HTMLElement>()
const imageCarousel = ref<any>(null)

const imagesList = computed(() => { return props.imagesList })
const currentProduct = computed(() => { return props.currentProduct })
const color = computed(() => { return props.color })
const preloadIndex = imagesList.value.findIndex(image => image?.file_url === currentProduct.value.thumb_url)

const { videos, isVideoTab, modifiedIndex } = useCampaignVideo(currentProduct, currentImage)
const { isInViewPort, viewImagesList, slideTo } = useCampaignImage(imagesList, currentProduct, currentImage, imageCarousel, viewZone, color, modifiedIndex, $emit, computed(() => props.campaignData))

watch(() => props.imagesList, () => {
  initState(props.imagesList, modifiedIndex.value)
}, { immediate: true })

watch(() => currentImage.value, () => {
  if (currentImage.value) {
    slideTo(currentImage.value)
  }
})

function getColor(imageIndex: number) {
  return colorsMap.value.get(imageIndex) || color.value
}

// 3D Mockup Support
const currentImgObject = computed(() => {
  return imagesList.value[currentImage.value ?? 0 - modifiedIndex.value]
})

const currentThreeDMockupData = computed(() => {
  if (!props.personalize) {
    return null
  }

  try {
    const mockupId = currentImgObject.value?.mockup_id
    if (mockupId && props.personalize.threeDMockupData) {
      return props.personalize.threeDMockupData.find(item => item.id === mockupId)
    }
  }
  catch (error) {
    console.error('Error getting currentThreeDMockupData', error)
    return null
  }
  return null
})

const isPersonalize3DCustomFullPrint = computed(() => {
  return props.campaignData?.personalized === 1
    && currentProduct.value?.full_printed === 2
    && currentThreeDMockupData.value
})

const hasMultipleViews = computed(() => {
  return imagesList.value && imagesList.value.length > 1
})
</script>

<template>
  <div
    ref="viewZone"
    class="flex relative w-full pt-[140%] lg:pt-[90%] xl:pt-[110%] viewzone"
  >
    <div
      v-if="(imagesList?.length + modifiedIndex) > 1 && $viewport.isGreaterOrEquals(VIEWPORT.desktop) && !isModal"
      class="overflow-y-auto w-[100px] p-1 absolute w-1/5 top-0 left-0 h-full"
    >
      <client-only>
        <VideoPlayTrigger
          v-for="(video, index) in videos"
          :key="video.url"
          :is-active="currentImage === index"
          :thumb-url="video.thumb"
          class="hover:(shadow-custom2)"
          @clicked="slideTo(index)"
        />
        <common-image
          v-for="(image, index) in imagesList"
          :key="index"
          :img-class="{
            'border border-white hover:(shadow-custom2) mb-2 !h-auto': true,
            '!border-primary': currentImage === index + modifiedIndex }"
          :image="{
            path: image?.file_url,
            color: getColor(index)
          }"
          :alt="campaignData?.name || currentProduct?.name"
          :fetchpriority="((index === modifiedIndex) ? 'high' : '')"
          @click="slideTo(index + modifiedIndex)"
        />
      </client-only>
    </div>
    <div
      class="w-full h-full absolute top-0 right-0"
      :class="{ 'lg:w-4/5': (imagesList?.length + modifiedIndex) > 1 && !isModal }"
    >
      <button
        v-if="!isShowDesign && !isVideoTab"
        class="absolute center-flex top-1 right-1 z-1 h-7 w-7 rounded-full btn bg-gray-300 hover:bg-gray-400"
        @click="isShowDesign ? '' : uiManager().viewImage(viewImagesList, currentImage)"
      >
        <i class="icon-sen-expand" />
      </button>
      <div v-if="isShowDesign">
        <template
          v-for="personalizeItem in currentProduct?.personalizeList"
        >
          <common-three-d-mockup-canvas
            v-if="isPersonalize3DCustomFullPrint"
            v-show="isShowDesign"
            :key="`${personalizeItem.personalizeKey}threeD${currentThreeDMockupData?.id}`"
            :is-modal="isModal"
            :mockup-data="currentThreeDMockupData"
            :design-data="personalizeItem.customDesign"
            :design="personalizeItem.designCanvas"
            :color="color"
            :type="personalizeItem.personalized"
            :show-navigation="hasMultipleViews"
            @update-mockup="(mockup) => personalizeItem.mockupCanvas = mockup"
            @finish-loading-design="personalizeItem.loaded = true"
          />
          <common-mockup-canvas
            v-else
            v-show="isShowDesign"
            :key="personalizeItem.personalizeKey"
            :is-show-design="true"
            :is-in-view-port="isInViewPort"
            :personalize="personalizeItem"
            :color="color"
            :custom-image="personalizeItem?.customImageList && personalizeItem?.customImageList[0]"
          />
        </template>
      </div>
      <client-only v-else-if="(imagesList?.length + modifiedIndex) > 1">
        <Splide
          ref="imageCarousel"
          :class="{ hidden: isShowDesign }"
          :options="{
            ...splideSetting,
            start: currentImage
          }"
          @splide:move="currentImage = $event.index"
        >
          <SplideSlide
            v-for="(video, index) in videos"
            :key="video.url"
            class="center-flex"
          >
            <CsVideoPlayer :video-url="video.url" :thumbnail-url="video.thumb" :autoplay="currentImage === index" />
          </SplideSlide>
          <SplideSlide
            v-for="(image, index) in imagesList"
            :key="index"
            class="center-flex"
          >
            <common-zoom-on-hover
              :image="{
                path: image?.file_url,
                type: 'full_hd',
                color: getColor(index)
              }"
              :alt="campaignData?.name || currentProduct?.name"
              :scale="1.6"
              :index="index"
              :preload-index="preloadIndex"
              @click="isShowDesign ? '' : uiManager().viewImage(viewImagesList, currentImage)"
            />
          </SplideSlide>
        </Splide>
      </client-only>
      <common-zoom-on-hover
        v-else
        :image="{
          path: imagesList && imagesList.length > 0 ? imagesList[0]?.file_url || currentProduct?.thumb_url : currentProduct?.thumb_url,
          type: 'full_hd',
          color
        }"
        :alt="campaignData?.name || currentProduct?.name"
        :scale="1.6"
        :index="0"
        @click="isShowDesign ? '' : uiManager().viewImage(viewImagesList, currentImage)"
      />
      <default-common-personalize-tag v-if="campaignData?.personalized && currentProduct?.personalized && (isInViewPort || isModal)" size="lg" :click-focus-personalize-input="true" />
    </div>
    <div v-if="currentProduct.full_printed === 4" class="flex justify-end mb-2 w-full" style="color: rgb(255, 145, 0);">
      <small>* {{ $t('Images are for illustrative purposes only, the actual embroidered product may differ from the illustrated image.') }}</small>
    </div>
  </div>
</template>

<style>
.\!\<md\:fixed .control-design {
  display: none;
}
</style>
