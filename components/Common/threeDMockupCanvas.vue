<script lang="ts" setup>
interface Props {
  isModal?: boolean
  mockupData?: ThreeDMockupData
  design?: any
  color?: string
  designData?: any
  mockupWidth?: number
  type?: number
  showNavigation?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isModal: false,
  mockupWidth: 500,
  type: 1,
  showNavigation: false
})

const emit = defineEmits<{
  updateMockup: [mockup: any]
  finishLoadingDesign: []
  nextMockup: []
  prevMockup: []
}>()

const { $MockupManager, $imgUrl, $config } = useNuxtApp()

const forceRender = ref(1)
const threeDMockupCanvas = ref<any>(null)
const isLoading = ref(true)
const previewMockup = ref<HTMLElement>()

onMounted(() => {
  loadMockup()
  window.addEventListener('resize', resetMockupSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', resetMockupSize)
})

watch(() => props.designData, () => {
  loadMockup()
})

watch(() => props.color, () => {
  changeColor()
})

function resetMockupSize() {
  // Handle mockup size reset on window resize
  if (threeDMockupCanvas.value) {
    const el = document.getElementById('viewBox')
    const newSize = el?.clientWidth * 1.25 || 500
    // Update mockup size if needed
  }
}

async function loadMockup() {
  forceRender.value++
  await nextTick()

  isLoading.value = true

  try {
    threeDMockupCanvas.value = new $MockupManager('preview_mockup')
    const el = document.getElementById('viewBox')

    const mockupParams: TMockupParams = {
      id: props.mockupData?.id || 'default',
      name: 'default',
      maxSize: el?.clientWidth * 1.25 || 500,
      designImg: props.designData?.file_url ? $imgUrl(props.designData.file_url, 'full_hd') : undefined,
      background: props.mockupData?.file_url ? $imgUrl(props.mockupData.file_url, 'full_hd') : undefined,
      color: props.color ?? '#ffffff',
      mockupType: props.designData?.type_detail ?? 'flat'
    }

    threeDMockupCanvas.value.locked = true
    emit('updateMockup', threeDMockupCanvas.value)

    if (props.mockupData?.design_json) {
      try {
        const designJson = JSON.parse(props.mockupData.design_json)
        mockupParams.name = designJson.name || 'default'

        if (designJson.color) {
          mockupParams.colorImg = $imgUrl({
            path: designJson.color,
            type: 'list'
          })
        }
        if (designJson.crop) {
          mockupParams.cropImg = $imgUrl({
            path: designJson.crop,
            type: 'list'
          })
        }
        if (designJson.shadow) {
          mockupParams.shadowImg = $imgUrl({
            path: designJson.shadow,
            type: 'list'
          })
        }
        if (designJson.glb) {
          mockupParams.glb = `${$config.public.cdnUrl}/${designJson.glb}`
        }

        await threeDMockupCanvas.value.loadMockup(mockupParams)
        threeDMockupCanvas.value.setActiveMockup(mockupParams.id)
      }
      catch (error) {
        console.error('Error parsing design JSON:', error)
      }
    }

    loadDesign()
  }
  catch (error) {
    console.error('Error loading 3D mockup:', error)
  }
  finally {
    isLoading.value = false
  }
}

function loadDesign() {
  if (props.type === 1) { // personalized
    if (props.design && threeDMockupCanvas.value) {
      const activeMockup = threeDMockupCanvas.value.getActiveMockup()
      if (activeMockup) {
        // Initial load if design has content
        if (props.design.hasDesign && props.design.hasDesign()) {
          activeMockup.updateDesign(props.design.exportCanvas())
        }

        // Set up the update callback
        if (props.design.onupdate) {
          props.design.onupdate = (design: any) => {
            if (design.exportCanvas) {
              activeMockup.updateDesign(design.exportCanvas())
            }
          }
        }
      }
    }
  }
  else if (props.type === 2) {
    // Full printed design
    if (props.design && threeDMockupCanvas.value) {
      const activeMockup = threeDMockupCanvas.value.getActiveMockup()
      if (activeMockup) {
        activeMockup.updateDesign(props.design)
      }
    }
  }
  emit('finishLoadingDesign')
}

function changeColor() {
  if (threeDMockupCanvas.value && props.color) {
    const activeMockup = threeDMockupCanvas.value.getActiveMockup()
    if (activeMockup && activeMockup.setColor) {
      activeMockup.setColor(props.color)
    }
  }
}

function goToNext() {
  emit('nextMockup')
}

function goToPrev() {
  emit('prevMockup')
}

function reRender() {
  if (props.design && props.design.renderAll) {
    props.design.renderAll()
  }
  if (props.design && props.design._onupdate) {
    props.design._onupdate()
  }
}
</script>

<template>
  <div :key="forceRender" class="mockup-canvas-item relative">
    <div id="preview_mockup" ref="previewMockup" class="three-d-canvas-container" />

    <!-- Navigation Arrows -->
    <div v-if="showNavigation" class="mockup-navigation-arrows">
      <div
        class="mockup-arrow prev-arrow transition-all duration-300"
        @click="goToPrev"
      >
        <i class="icon-sen-chevron-left absolute-center" />
      </div>
      <div
        class="mockup-arrow next-arrow transition-all duration-300"
        @click="goToNext"
      >
        <i class="icon-sen-chevron-right absolute-center" />
      </div>
    </div>

    <div v-if="isLoading" class="absolute-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.mockup-canvas-item {
  position: relative;
  width: 100%;
  height: auto;
}

.three-d-canvas-container {
  width: 100%;
  height: auto;
  min-height: 300px;
}

.mockup-navigation-arrows {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 10;
}

.mockup-arrow {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: scale(1.1);
  }

  i {
    font-size: 18px;
    color: #333;
  }
}

.prev-arrow {
  margin-left: 10px;
}

.next-arrow {
  margin-right: 10px;
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
}
</style>
