import type PersonalizeCustom from '~/components/default/campaign/personalizeCustom.vue'
import type PersonalizeCustomOptions from '~/components/default/campaign/personalizeCustomOptions.vue'
import type PersonalizePB from '~/components/default/campaign/personalizePb.vue'

import { useCampaignStore } from '~/store/campaign'

export function useCampaignPersonalize(campaignData: Campaign, userCampaignOption: Partial<UserCampaignOption>, isModal = false) {
  const {
    getCustomDesign
  } = useCampaignStore()
  const { AWSUpload } = useAWSUpload()
  const { $imgUrl } = useNuxtApp()
  const localePath = useLocalePath()

  const personalizeCustom = ref<InstanceType<typeof PersonalizeCustom>>()
  const personalizePB = ref<InstanceType<typeof PersonalizePB>>()
  const personalizeCustomOptions = ref<InstanceType<typeof PersonalizeCustomOptions>>()

  const isShowDesign = ref(false)
  const showingDesign = ref('')
  const currentPersonalize = shallowRef<Personalize>()

  const baseMockupList = shallowRef<BaseMockup[]>([])
  const customDesignList = shallowRef<CustomDesign[]>([])
  const pbArtworkList = shallowRef<PbArtwork[]>([])
  const threeDMockupList = shallowRef<ThreeDMockupData[]>([])

  if (campaignData?.personalized === 1 || campaignData?.personalized === 2) {
    useHead({
      script: [{
        hid: 'design-canvas',
        src: `${cdnURL.value}design-canvas/design-canvas.js?v=1`,
        // @ts-ignore
        body: true
      }]
    })
  }

  watch(() => userCampaignOption.currentOptions?.size, () => {
    if (userCampaignOption.currentProduct?.full_printed) {
      getProductDesign()
    }
  })

  watch(() => userCampaignOption.currentProduct, () => {
    if (userCampaignOption.currentProduct) {
      getProductDesign()
    }
  })

  async function getDataCustomDesign() {
    const data = await getCustomDesign(campaignData.slug as string)

    baseMockupList.value = toRaw(data?.base_mockup || [])
    customDesignList.value = toRaw(data?.custom_designs || [])
    pbArtworkList.value = toRaw(data?.pb_artworks || [])
    threeDMockupList.value = toRaw(data?.three_d_mockups || [])

    campaignData.products?.forEach((product) => {
      if (product.personalizeList?.length) {
        return
      }

      if (campaignData.personalized === 1 && customDesignList.value.length) {
        // Check if any product has full_printed === 2 (3D mockup support)
        const hasFullPrinted = campaignData.products?.some(p => p.full_printed === 2) || false

        product.personalizeList = customDesignList.value.filter((customDesign) => {
          return customDesign.product_id === product.id
        }).map((customDesign) => {
          const personalizeKey = `design_${customDesign?.product_id}_${customDesign?.print_space}`
          const baseMockup = baseMockupList.value.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === customDesign.print_space)

          return {
            personalizeKey,
            personalized: 1,
            customDesign,
            baseMockup,
            threeDMockupData: hasFullPrinted ? threeDMockupList.value : undefined
          }
        })
      }
      else if (campaignData.personalized === 2 && pbArtworkList.value.length) {
        // Check if any product has full_printed === 2 (3D mockup support)
        const hasFullPrinted = campaignData.products?.some(p => p.full_printed === 2) || false

        product.personalizeList = pbArtworkList.value.filter((pbArtwork) => {
          return pbArtwork.product_id === product.id
        }).map((pbArtwork) => {
          const baseMockup = baseMockupList.value.find(mockup => product && mockup.product_id === product.template_id && mockup.print_space === pbArtwork.print_space)
          const personalizeKey = `design_${pbArtwork?.product_id}_${pbArtwork?.print_space}`
          return {
            personalizeKey,
            personalized: 2,
            pbArtwork,
            baseMockup,
            threeDMockupData: hasFullPrinted ? threeDMockupList.value : undefined
          }
        })
      }
    })

    if (userCampaignOption?.currentProduct) {
      getProductDesign()
    }
  }

  function setCurrentPersonalize() {
    const product = userCampaignOption.currentProduct as Product
    const target = product.personalizeList?.find(personalize =>
      personalize.customDesign?.print_space
      && (
        (showingDesign.value !== '')
          ? personalize.personalizeKey === showingDesign.value
          : (userCampaignOption.currentOptions?.size || userCampaignOption.optionList?.size?.[0] || userCampaignOption.optionListFull?.size?.[0])?.startsWith(personalize.customDesign?.print_space)
      )
    )
    currentPersonalize.value = target || product.personalizeList?.[0]
  }

  const currentProduct = computed(() => userCampaignOption.currentProduct as Product)
  const { preLoadDesign, initPersonalizePb, resetPersonalizeCustomData } = useProductDesign(currentProduct, isModal)

  watch([
    isShowDesign,
    showingDesign
  ], setCurrentPersonalize)

  async function getProductDesign() {
    const product = userCampaignOption.currentProduct as Product

    if (product.personalized === 1) {
      if (product.customItemList?.length && !product.full_printed) {
        if (!isModal) {
          product.personalizeList?.forEach((personalize) => {
            setTimeout(() => {
              resetPersonalizeCustomData(personalize, product)
            }, 0)
          })
          let checkHasData = false
          product.personalizeList?.forEach((personalize) => {
            const data = resetPersonalizeCustomData(personalize, product)
            if (data) {
              checkHasData = true
            }
          })
          if (checkHasData) {
            showDesignHandler(product.personalizeList?.[0]?.personalizeKey as string)
          }
        }
      }
      else {
        product.customTextList = shallowReactive([])
        product.customImageList = shallowReactive([])
        product.customItemList = shallowReactive([])

        const currentSize = userCampaignOption.currentOptions?.size || userCampaignOption.optionList?.size?.[0] || userCampaignOption.optionListFull?.size?.[0]
        if (product.personalizeList) {
          for (let i = 0; i < product.personalizeList.length; i++) {
            const personalize = product.personalizeList[i]
            const personalizeLength = product.personalizeList.length

            if (
              personalizeLength > 1
              && personalize.customDesign?.print_space !== 'default'
              && product.full_printed
              && personalize.customDesign?.print_space
              && currentSize
              && !currentSize.startsWith(personalize.customDesign?.print_space)
            ) {
              continue
            }
            personalize.customTextList = shallowReactive([])
            personalize.customImageList = shallowReactive([])
            personalize.customItemList = shallowReactive([])
            isShowDesign.value = await preLoadDesign(personalize) || false
            personalize.customItemList = product.customTextList?.concat(product.customImageList || [])
          }
        }
        if (isShowDesign.value) {
          showDesignHandler(product.personalizeList?.[0]?.personalizeKey as string)
        }
        product.customItemList = product.customTextList?.concat(product.customImageList || [])
      }
    }

    if (product.personalized === 2 && product.personalizeList?.length) {
      await initPersonalizePb(product.personalizeList[0])
    }
  }

  async function updatePersonalizeCustom(customItem: CustomItem, value: string | File) {
    if (customItem.data.type === 'i-text') {
      const { data, personalize, product, subPersonalize } = customItem as CustomTextItem
      const customText = data

      customText.text = removeEmoji(value as string)
      const designCanvas = personalize.designCanvas

      // Support for multi-object text updates (3D mockups)
      if (designCanvas.getAllObjectsByName) {
        const currentObjects = designCanvas.getAllObjectsByName(data.name)
        currentObjects.forEach((currentObject: any) => {
          designCanvas.updateText(currentObject, customText.text)
        })
      } else {
        // Fallback to single object update
        const currentObject = designCanvas.getObjectByName(data.name)
        designCanvas.updateText(currentObject, customText.text)
      }

      if (subPersonalize) {
        const subCanvasDesign = subPersonalize.designCanvas
        if (subCanvasDesign.getAllObjectsByName) {
          const subObjects = subCanvasDesign.getAllObjectsByName(data.name)
          subObjects.forEach((subObject: any) => {
            subCanvasDesign.updateText(subObject, customText.text)
          })
        } else {
          // Fallback to single object update
          const subObject = subCanvasDesign.getObjectByName(data.name)
          subCanvasDesign.updateText(subObject, customText.text)
        }
      }

      if (customText.text && !product.isChangeCustomDesign && product.customDesignType === 'by_alphabet') {
        const firstLetter = customText.text.trim().charAt(0).toLowerCase()
        if (firstLetter && product.selectedCustomDesign !== firstLetter && $customDesignArray.by_alphabet.includes(firstLetter)) {
          selectCustomDesign(product, firstLetter, false)
        }
      }
    }
    else if (customItem.data.type === 'image') {
      const customImage = customItem as CustomImageItem
      uiManager().$patch({ isUploadFile: true })
      const upload = await AWSUpload(value as File)
      uiManager().$patch({ isUploadFile: false })
      if (upload) {
        customImage.currentFileUploadUrl = upload
        customImage.isConfirmQuantityImage = false
        customImage.personalize.designCanvas.changeCustomImage($imgUrl({
          path: upload,
          type: 'full'
        })).then(() => {
          customImage.canvasObject = shallowReactive(customItem.personalize.designCanvas.getCustomImage())
        })
        if (!isModal) {
          const query = { ...useRoute().query }
          query['custom-image'] = btoa(upload as string)
          useRouter().replace(localePath({ fullPath: useRoute().fullPath, query }))
        }
      }

      if (Object.hasOwnProperty.call(personalizeCustom?.value, 'editCustomImage')) {
        personalizeCustom.value.editCustomImage = customItem
      }
    }
  }

  async function selectCustomDesign(product: Product, value: string, isUserSelected = false) {
    if (product?.personalizeCustomDesign) {
      loading('selectCustomDesign')
      await product?.personalizeCustomDesign.designCanvas.changeDesignByCharacter(value)
      product.selectedCustomDesign = value
      if (isUserSelected) {
        product.isChangeCustomDesign = true
      }
      loading(false)
      if (!isModal) {
        const query = { ...useRoute().query }
        query.customdesign = value
        useRouter().replace(localePath({ query }))
      }
    }
  }

  async function checkPersonalize(): Promise<CheckProductResult> {
    // Even the data is incorrect, we still allow the customer checkout with the error'd product
    // Will resolve the error at customer's support

    // if (!userCampaignOption.currentProduct?.personalizeList?.length) {
    //   return {
    //     success: true,
    //     product: userCampaignOption.currentProduct
    //   }
    // }
    if (campaignData.personalized === 1 && userCampaignOption.currentProduct) {
      const { customTextList, customImageList } = userCampaignOption.currentProduct
      let { personalizeList } = userCampaignOption.currentProduct

      if (userCampaignOption.currentProduct.full_printed) {
        if (personalizeList && personalizeList.length && personalizeList[0].customDesign?.print_space !== 'default') {
          personalizeList = personalizeList.filter(personalize => userCampaignOption.currentOptions?.size?.startsWith(personalize.customDesign?.print_space as string))
        }
      }

      if (customTextList?.length) {
        const checkInputText = customTextList.filter(item => !item.data.text)
        if (checkInputText?.length) {
          return {
            success: false,
            customItem: checkInputText[0],
            product: userCampaignOption.currentProduct
          }
        }
      }
      if (customImageList) {
        const checkCustomImage = customImageList.filter(item => !item.currentFileUploadUrl)
        if (checkCustomImage?.length) {
          return {
            success: false,
            customItem: checkCustomImage[0],
            product: userCampaignOption.currentProduct
          }
        }
      }
      return {
        success: true,
        designDataUrl: personalizeList?.map(personalize => personalize.mockupCanvas?.toDataURL('png'))
      }
    }
    else if (campaignData.personalized === 2 && userCampaignOption.currentProduct?.personalizeList?.length) {
      const personalized = userCampaignOption.currentProduct?.personalizeList[0]
      const validated = await window.pbsdk.validate(personalized.personalizeKey)

      if (validated) {
        return {
          success: true,
          designDataUrl: [personalized.mockupCanvas.toDataURL('png')],
          product: userCampaignOption.currentProduct
        }
      }

      return {
        success: false,
        product: userCampaignOption.currentProduct
      }
    }
    else {
      return {
        success: false,
        product: userCampaignOption.currentProduct
      }
    }
  }

  function showDesignHandler(data: string) {
    isShowDesign.value = true
    showingDesign.value = data
  }

  async function handlePersonalizeError({ product, customItem, errorElementKey, errorType }: CheckProductResult, currentBundleProduct: Ref<any>) {
    currentBundleProduct.value = undefined
    await nextTick()
    if (product?.isBundle) {
      currentBundleProduct.value = product
    }
    else {
      if (product?.personalized === 1 && customItem) {
        isShowDesign.value = true
        personalizeCustom.value!.currentCustomItem = customItem
        personalizeCustom.value!.isOnSelectCustomDesign = false
      }
      else if (product?.personalized === 2) {
        isShowDesign.value = true
        personalizePB.value!.toggleOptionMobile = true
      } if (product?.personalized === 3 || product?.full_printed === 5) {
        personalizeCustomOptions.value!.toggleOptionMobile = true
      }
    }
    await nextTick()
    if (product?.personalized === 1 && customItem) {
      if (customItem.data.type === 'i-text') {
        setTimeout(() => {
          const errorItem = document.getElementById(`customText_${product?.isBundle ? 'modal' : 'campaign'}_${customItem.data.name}`)
          errorItem?.focus()
        }, product?.isBundle ? 0 : 400)
      }
      else if (customItem.data.type === 'image') {
        setTimeout(() => {
          const errorItem = document.getElementById(`customImage_${product?.isBundle ? 'modal' : 'campaign'}_0`)?.parentElement
          errorItem?.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }, product?.isBundle ? 0 : 400)
      }
    }
    else if (product?.personalized === 2) {
      try {
        const errorItem = document.getElementsByClassName('ant-form-explain')[0]
        const isInViewport = checkViewPort(errorItem as HTMLElement)
        if (!isInViewport) {
          errorItem.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
        }
      }
      catch (error) {
        console.log(error)
      }
    }
    else if (product?.personalized === 3 || product?.full_printed === 5) {
      setTimeout(() => {
        if (errorElementKey) {
          const errorElement: HTMLElement | null = document.getElementById(errorElementKey)
          if (errorElement) {
            if (errorType === CUSTOM_OPTION_TYPE.text) {
              (errorElement as HTMLInputElement).focus()
            }
            if (errorType === CUSTOM_OPTION_TYPE.image) {
              errorElement.parentElement?.scrollIntoView()
            }
            else {
              errorElement.scrollIntoView()
            }
          }
        }
      }, product?.isBundle ? 0 : 400)
    }
  }

  return {
    personalizeCustom,
    personalizePB,
    personalizeCustomOptions,

    isShowDesign,
    showingDesign,
    currentPersonalize,
    showDesignHandler,
    getDataCustomDesign,
    getProductDesign,
    updatePersonalizeCustom,
    selectCustomDesign,
    checkPersonalize,
    handlePersonalizeError,
    setCurrentPersonalize
  }
}

export function useControlCustomImage(customImageItem: CustomImageItem) {
  const designCanvas = customImageItem.personalize.designCanvas

  const controlList = [{
    key: 'zoomOut',
    icon: 'icon-sen-magnify-minus-outline'
  }, {
    key: 'zoomIn',
    icon: 'icon-sen-magnify-plus-outline'
  }, {
    key: 'rollRight',
    icon: 'icon-sen-refresh',
    style: 'transform: rotateY(180deg)'
  }, {
    key: 'rollLeft',
    icon: 'icon-sen-refresh'
  }, {
    key: 'moveRight',
    icon: 'icon-sen-arrow-right-thin'
  }, {
    key: 'moveLeft',
    icon: 'icon-sen-arrow-left-thin'
  }, {
    key: 'moveDown',
    icon: 'icon-sen-arrow-down-thin'
  }, {
    key: 'moveUp',
    icon: 'icon-sen-arrow-up-thin'
  }]

  function controlCustomImage(key: string) {
    const canvasObject = customImageItem.canvasObject
    switch (key) {
      case 'zoomOut':
        canvasObject!.left = (canvasObject?.left ?? 0) + ((canvasObject?.width ?? 0) * (canvasObject?.scaleX ?? 0) * 0.02 * (Math.cos((canvasObject?.angle ?? 0) * Math.PI / 180) || 1))
        canvasObject!.top = (canvasObject?.top ?? 0) + ((canvasObject?.height ?? 0) * (canvasObject?.scaleY ?? 0) * 0.02 * (Math.cos((canvasObject?.angle ?? 0) * Math.PI / 180) || 1))
        canvasObject?.scale((canvasObject?.scaleY ?? 0) / 1.04)
        checkScale()
        break
      case 'zoomIn':
        canvasObject?.scale((canvasObject?.scaleY ?? 0) * 1.04)
        canvasObject!.left = (canvasObject?.left ?? 0) - ((canvasObject?.width ?? 0) * (canvasObject?.scaleX ?? 0) * 0.02 * (Math.cos((canvasObject?.angle ?? 0) * Math.PI / 180) || 1))
        canvasObject!.top = (canvasObject?.top ?? 0) - ((canvasObject?.height ?? 0) * (canvasObject?.scaleY ?? 0) * 0.02 * (Math.cos((canvasObject?.angle ?? 0) * Math.PI / 180) || 1))
        checkScale()
        break
      case 'rollRight':
        canvasObject?.rotate((canvasObject?.angle ?? 0) - 1)
        break
      case 'rollLeft':
        canvasObject?.rotate((canvasObject?.angle ?? 0) + 1)
        break
      case 'moveRight':
        canvasObject!.left = (canvasObject?.left ?? 0) + 10
        break
      case 'moveLeft':
        canvasObject!.left = (canvasObject?.left ?? 0) - 10
        break
      case 'moveDown':
        canvasObject!.top = (canvasObject?.top ?? 0) + 10
        break
      case 'moveUp':
        canvasObject!.top = (canvasObject?.top ?? 0) - 10
    }
    designCanvas.renderAll()
    designCanvas._onupdate()
  }

  designCanvas.on('object:scaling', checkScale)

  let timeoutControl: any
  let intervalControl: any

  function startIntervalControl(key: string) {
    removeIntervalControl()
    timeoutControl = setTimeout(() => {
      intervalControl = setInterval(() => {
        controlCustomImage(key)
      }, 50)
    }, 500)
  }

  function removeIntervalControl() {
    if (timeoutControl) {
      clearTimeout(timeoutControl)
    }
    if (intervalControl) {
      clearInterval(intervalControl)
    }
  }

  const imageResolution = shallowRef({
    height: Math.round((customImageItem.canvasObject?.height ?? 0) * (customImageItem.canvasObject?.scaleY ?? 0) / 2),
    width: Math.round((customImageItem.canvasObject?.width ?? 0) * (customImageItem.canvasObject?.scaleX ?? 0) / 2)
  })

  function checkScale() {
    if (customImageItem.canvasObject && ((customImageItem.canvasObject.scaleX ?? 0) > 2 || (customImageItem.canvasObject.scaleY ?? 0) > 2)) {
      customImageItem.isImageLowQuality = true
    }
    else {
      customImageItem.isImageLowQuality = false
    }
    imageResolution.value = {
      height: Math.round((customImageItem.canvasObject?.height ?? 0) * (customImageItem.canvasObject?.scaleY ?? 0) / 2),
      width: Math.round((customImageItem.canvasObject?.width ?? 0) * (customImageItem.canvasObject?.scaleX ?? 0) / 2)
    }
  }

  return {
    imageResolution,
    controlList,
    controlCustomImage,
    startIntervalControl,
    removeIntervalControl
  }
}

export function updateDesignQuery({ data }: CustomTextItem) {
  const localePath = useLocalePath()
  const query = { ...useRoute().query }
  query[data.name?.replaceAll(' ', '-') || 'custom-text'] = data.text as string
  useRouter().replace(localePath({ query }))
}

export async function getPersonalizeUpload(personalizeList: { [key: string]: Personalize }) {
  const { AWSUpload } = useAWSUpload()
  const awsUploadList = Object.values(personalizeList).map(({ mockupCanvas, customDesign }) => {
    if (!mockupCanvas) {
      return false
    }
    const designID = `design_${customDesign?.product_id}_${customDesign?.print_space}.png`
    const canvasElement: HTMLCanvasElement = mockupCanvas.getElement()
    return getImageFileFromCanvas(canvasElement).then((blob) => {
      if (!blob) {
        return null
      }
      const file = new File([blob], designID, { type: 'image/png' })
      return AWSUpload(file)
    })
  })
  const uploadUrls = await Promise.all(awsUploadList)
  const result: { [key: string]: any } = {}
  Object.keys(personalizeList).forEach((key, index) => {
    result[key] = uploadUrls[index]
  })
  return result
}

function removeEmoji(text: string) {
  return text.replace(/([\u2700-\u27BF\uE000-\uF8FF\u2011-\u26FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|\uD83E[\uDD10-\uDDFF])/g, '')
}

function getImageFileFromCanvas(canvas: HTMLCanvasElement): Promise<Blob | null> {
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      resolve(blob)
    })
  })
}

export function useCampaignPersonalizeCustomOptions(customOptions?: ComputedRef<PersonalizeCustomOptions | undefined> | PersonalizeCustomOptions, isModal = false, personalized?: 0 | 1 | 2 | 3 | undefined, full_printed?: 0 | 1 | 2 | 3 | 4 | 5 | undefined, commonOptions?: ComputedRef<PersonalizeCommonOptions | undefined> | PersonalizeCommonOptions) {
  const userCustomOptions = reactive<Array<Array<PersonalizeCustomOptionsItem>>>([])
  const userCommonOptions = reactive<Array<Array<PersonalizeCustomOptionsItem>>>([])
  const extraCustomFee = ref(Number(toValue(customOptions)?.group?.extra_custom_fee) || 0)
  const extraCommonCustomFee = ref(Number(toValue(commonOptions)?.extra_custom_fee) || 0)
  const totalExtraCustomFee = ref(0)
  const groupCustomOptionsQuantity = ref(1)

  if (toValue(customOptions)?.group?.limit) {
    resetOption()
  }
  if (toValue(commonOptions)?.options?.length) {
    resetCommonOption()
  }
  function requiredValue(value: any) {
    return Number.isNaN(value) ? 0 : Number(value)
  }
  function mapOptions(options: PersonalizeCustomOptionsItem[]) {
    return JSON.parse(JSON.stringify(options)).map((g: PersonalizeCustomOptionsItem) => {
      g.unrequired = g?.unrequired || false
      g.price = g?.price ? g.price : 0
      if (g.type === CUSTOM_OPTION_TYPE.text) {
        g.placeholder = g?.value
        g.value = null
        g.max_length = g?.max_length ?? null
      }
      if (g.type === CUSTOM_OPTION_TYPE.dropdown) {
        g.value = null
      }
      if (g.type === CUSTOM_OPTION_TYPE.image) {
        g.imagePath = null
      }
      return g
    })
  }
  const totalCustomOptionFee = computed(() => {
    return totalExtraCustomFee.value
  })
  function resetOption() {
    totalExtraCustomFee.value = 0
    userCustomOptions.length = 0
    extraCustomFee.value = Number(toValue(customOptions)?.group?.extra_custom_fee) || 0
    const customOptionsValue = toValue(customOptions)
    const query = { ...useRoute().query }
    if (!isModal && query.groups && customOptionsValue?.group?.limit && Number.parseInt(query.groups as string) < toValue(customOptions as PersonalizeCustomOptions)?.group.limit) {
      groupCustomOptionsQuantity.value = Number.parseInt(query.groups as string) || 1
    }
    if (customOptionsValue?.options) {
      const defaultCustomOptions = mapOptions(customOptionsValue?.options) as Array<PersonalizeCustomOptionsItem>
      for (let groupNumber = 1; groupNumber <= customOptionsValue.group.limit; groupNumber++) {
        const optionData = JSON.parse(JSON.stringify(defaultCustomOptions)) as Array<PersonalizeCustomOptionsItem>
        optionData.forEach((option, optionIndex) => {
          option.g_index = groupNumber - 1
          option.g_type = 'group'
          if (!option?.placeholder) {
            option.placeholder = option.value
          }
          if (option.type === CUSTOM_OPTION_TYPE.text) {
            option.max_length = option?.max_length ? option.max_length : null
          }
          const key = `${option.type}_${groupNumber}_${optionIndex}`
          if (groupCustomOptionsQuantity.value >= groupNumber) {
            option.value = (query[key] && !isModal) ? decodeURIComponent(query[key] as string) : ''
            if (option.type === CUSTOM_OPTION_TYPE.image) {
              option.imagePath = option.value as string
            }
          }
          else {
            option.value = ''
            if (option.type === CUSTOM_OPTION_TYPE.image) {
              option.imagePath = ''
            }
            delete query[key]
          }
        })
        userCustomOptions.push(optionData)
      }
      if (!isModal) {
        useRouter().replace({
          query: {
            ...query
          }
        })
      }
      calculateCustomOptionsFee()
    }
  }
  function resetCommonOption() {
    totalExtraCustomFee.value = 0
    userCommonOptions.length = 0
    extraCommonCustomFee.value = Number(toValue(commonOptions)?.extra_custom_fee) || 0
    const commonOptionsValue = toValue(commonOptions)
    const query = { ...useRoute().query }
    if (commonOptionsValue?.options) {
      const defaultOptions = mapOptions(commonOptionsValue?.options) as Array<PersonalizeCustomOptionsItem>
      const optionData = JSON.parse(JSON.stringify(defaultOptions)) as Array<PersonalizeCustomOptionsItem>
      optionData.forEach((option, optionIndex) => {
        option.g_type = 'common'
        if (!option?.placeholder) {
          option.placeholder = option.value
        }
        if (option.type === CUSTOM_OPTION_TYPE.text) {
          option.max_length = option?.max_length ? option.max_length : null
        }
        const key = `c_${option.type}_1_${optionIndex}`
        option.value = (query[key] && !isModal) ? decodeURIComponent(query[key] as string) : ''
        if (option.type === CUSTOM_OPTION_TYPE.image) {
          option.imagePath = option.value as string
        }
      })
      userCommonOptions.push(optionData)
      if (!isModal) {
        useRouter().replace({
          query: {
            ...query
          }
        })
      }
      calculateCustomOptionsFee()
    }
  }
  function updateCustomOptions(data: {
    groupNumber: number
    optionIndex: number
    value: any
    type: number
  }) {
    const { groupNumber, optionIndex, type } = data
    let { value } = data
    const customOption = userCustomOptions[(groupNumber - 1)][optionIndex]
    const newQuery: { [key: string]: string } = {}
    if (customOption.type === CUSTOM_OPTION_TYPE.text) {
      let totalLength = value?.length ?? 0
      const maxLength = customOption.max_length ?? null
      if (type === 2) {
        if (totalLength > 0 && value?.trim()?.length === 0) {
          value = ''
          totalLength = 0
        }
        value = value.trim()
        totalLength = value.length
      }
      if (maxLength && totalLength > maxLength) {
        value = value.slice(0, maxLength)
      }
    }
    customOption.g_index = groupNumber - 1
    customOption.g_type = 'group'
    customOption.value = value
    if (customOption.type === CUSTOM_OPTION_TYPE.image) {
      customOption.imagePath = value
    }
    const key = `${customOption.type}_${groupNumber}_${optionIndex}`
    newQuery[key] = value

    window.dispatchEvent(new CustomEvent('sp_custom_input_change', { detail: `Input: ${customOption.label} | Value: ${customOption?.value || customOption?.imagePath}` }))
    if (!isModal) {
      useRouter().replace({
        query: {
          ...useRoute().query,
          ...newQuery
        }
      })
    }
    checkPersonalizeCustomOptions(false)
  }
  function updateCommonOptions(data: {
    optionIndex: number
    value: any
    type: number
  }) {
    const { optionIndex, type } = data
    let { value } = data
    const customOption = userCommonOptions[0][optionIndex]
    const newQuery: { [key: string]: string } = {}
    if (customOption.type === CUSTOM_OPTION_TYPE.text) {
      let totalLength = value?.length ?? 0
      const maxLength = customOption.max_length ?? null
      if (type === 2) {
        if (totalLength > 0 && value?.trim()?.length === 0) {
          value = ''
          totalLength = 0
        }
        value = value.trim()
        totalLength = value.length
      }
      if (maxLength && totalLength > maxLength) {
        value = value.slice(0, maxLength)
      }
    }
    customOption.g_type = 'common'
    customOption.value = value
    if (customOption.type === CUSTOM_OPTION_TYPE.image) {
      customOption.imagePath = value
    }
    const key = `c_${customOption.type}_1_${optionIndex}`
    newQuery[key] = value

    window.dispatchEvent(new CustomEvent('sp_custom_input_change', { detail: `Input: ${customOption.label} | Value: ${customOption?.value || customOption?.imagePath}` }))
    if (!isModal) {
      useRouter().replace({
        query: {
          ...useRoute().query,
          ...newQuery
        }
      })
    }
    checkPersonalizeCustomOptions(false)
  }

  function updateGroupCustomOptionsQuantity(value: number) {
    const query = { ...useRoute().query }
    groupCustomOptionsQuantity.value = value
    Object.keys(query).forEach((key) => {
      if (key.startsWith('text') || key.startsWith('dropdown') || key.startsWith('image')) {
        const match = key.match(/_(\d+)_/)
        if (match) {
          const groupNumber = Number.parseInt(match[1])
          if (value < groupNumber) {
            const customOptions = userCustomOptions[groupNumber - 1]
            customOptions?.forEach((option) => {
              option.value = ''
              if (option.type === CUSTOM_OPTION_TYPE.image) {
                option.imagePath = ''
              }
            })
            delete query[key]
          }
        }
      }
    })
    checkPersonalizeCustomOptions()
    if (!isModal) {
      useRouter().replace({
        query: {
          ...query,
          groups: value
        }
      })
    }
  }

  function calculateCustomOptionsFee() {
    let totalFee = 0
    const hasCommonOptions = userCommonOptions.some(group => group.some(option => option?.value && option?.value?.length > 0))
    if (personalized === 0 && full_printed === 5) {
      userCustomOptions.filter(group => group.some(option => option?.value || option?.imagePath)).forEach((group, idx) => {
        if (group.some(option => option?.value || option?.imagePath) && idx > 0) {
          totalFee += extraCustomFee.value
        }
        group.forEach((option) => {
          if ((option?.value || option?.imagePath) && option?.price && option.price > 0 && idx > 0) {
            totalFee += Number.parseFloat(String(option.price))
          }
        })
      })
    }
    else {
      userCustomOptions.forEach((group, idx) => {
        if (group.some(option => option?.value || option?.imagePath) && idx > 0) {
          totalFee += extraCustomFee.value
        }
      })
    }
    if (hasCommonOptions) {
      totalFee += extraCommonCustomFee.value
    }
    totalExtraCustomFee.value = totalFee
  }

  function checkPersonalizeCustomOptions(showNotification: boolean = true): CheckProductResult {
    let firstErrorElementKey: string | null = null
    let firstErrorType: 'text' | 'dropdown' | 'image' | null = null

    let check = userCustomOptions.filter((item, index) => index < groupCustomOptionsQuantity.value)
      .every((groupOption, groupNumber) => {
        return groupOption.every((customOption, optionIndex) => {
          if ((!customOption.value && !customOption.unrequired) && !firstErrorElementKey) {
            firstErrorElementKey = `customOption_${customOption.type}_${groupNumber + 1}_${optionIndex}`
            firstErrorType = customOption.type
          }
          if (customOption.unrequired) {
            return true
          }
          return !!customOption.value
        })
      })

    if (check && userCommonOptions && userCommonOptions.length) {
      check = userCommonOptions.every((groupOption) => {
        return groupOption.every((option, optionIndex) => {
          if ((!option.value && !option.unrequired) && !firstErrorElementKey) {
            firstErrorElementKey = `commonOption_${option.type}_1_${optionIndex}`
            firstErrorType = option.type
          }
          if (option.unrequired) {
            return true
          }
          return !!option.value
        })
      })
    }
    let hasCommonOptions = true
    const hasOptionsValue = userCustomOptions.filter((i, index) => index < groupCustomOptionsQuantity.value)
      .some((groupOption) => {
        return groupOption.some(customOption => customOption.value || customOption.unrequired)
      })
    if (userCommonOptions && userCommonOptions.length) {
      hasCommonOptions = userCommonOptions.some((groupOption) => {
        return groupOption.some(option => option.value || option.unrequired)
      })
    }
    check = (hasOptionsValue || hasCommonOptions) ? check : false
    calculateCustomOptionsFee()
    if (!hasCommonOptions && !hasOptionsValue && showNotification) {
      const { $i18n } = useNuxtApp()
      uiManager().createPopup($i18n.t('Please select at least one option') as string, 'error')
    }

    return {
      success: check,
      errorElementKey: firstErrorElementKey,
      errorType: firstErrorType
    }
  }

  return {
    extraCustomFee,
    groupCustomOptionsQuantity,
    userCustomOptions,
    userCommonOptions,
    totalCustomOptionFee,
    updateCustomOptions,
    updateCommonOptions,
    resetOption,
    resetCommonOption,
    requiredValue,
    updateGroupCustomOptionsQuantity,
    checkPersonalizeCustomOptions
  }
}
