declare global {
  interface ThreeDMockupData {
    id: number
    campaign_id: number
    product_id: number
    mockup_id: number
    file_url: string
    design_json: string
    print_space: string
    type: string
    type_detail: string
  }

  interface PersonalizeResponse {
    base_mockup: BaseMockup[]
    custom_designs: CustomDesign[]
    pb_artworks: PbArtwork[]
    three_d_mockups?: ThreeDMockupData[]
  }

  interface TMockupParams {
    id: string | number
    name?: string
    maxSize?: number
    designImg?: string
    background?: string
    color?: string
    mockupType?: string
    colorImg?: string
    cropImg?: string
    shadowImg?: string
    glb?: string
    printWidth?: number
    printHeight?: number
    canvasEl?: HTMLCanvasElement
  }

  interface TMockupRender {
    mockup: any
    scene: any
    camera: any
    renderer: any
    print: any
    material: any
    container: HTMLElement
    locked: boolean
    
    render(): Promise<boolean>
    render3D(): void
    hide(): void
    show(): void
    setColor(color: string): void
    updateDesign(designImg: string): void
    exportCanvas(): HTMLCanvasElement | null
  }

  interface TMockupLoader {
    loadMockup(params: TMockupParams): Promise<any>
  }

  interface MockupManager {
    container: HTMLElement
    mockupLoader: TMockupLoader
    mockups: { [key: string]: TMockupRender }
    activeMockup: TMockupRender | null
    locked: boolean

    loadMockup(params: TMockupParams): Promise<TMockupRender>
    getMockup(id: string): TMockupRender | false
    getActiveMockup(): TMockupRender | null
    setActiveMockup(id: string): void
  }

  // Extend existing Personalize interface to include 3D mockup data
  interface Personalize {
    personalized?: 0 | 1 | 2 | 3
    imagePreloaded?: boolean
    loaded?: boolean
    baseMockup?: BaseMockup
    customDesign?: CustomDesign
    pbArtwork?: PbArtwork
    mockupCanvas?: DesignCanvas.MockupCanvas
    designCanvas?: DesignCanvas.DesignCanvas
    threeDMockupData?: ThreeDMockupData[]

    personalizeKey?: string

    customTextList?: Array<CustomTextItem>
    customImageList?: Array<CustomImageItem>
    customItemList?: Array<CustomItem>

    isChangeCustomDesign?: boolean
    customDesignType?: 'by_alphabet' | 'by_month' | 'by_year' | 'custom'
    customDesignList?: Array<string>
    selectedCustomDesign?: string

    pbCustomInfo?: any
    pbPrintUrl?: string
  }

  // Global Three.js types
  namespace THREE {
    class Scene {}
    class PerspectiveCamera {}
    class WebGLRenderer {}
    class CanvasTexture {}
    class MeshBasicMaterial {}
    class Vector2 {}
    class DirectionalLight {}
    class AmbientLight {}
  }

  // Global window extensions
  interface Window {
    DesignCanvas: any
    THREE: typeof THREE
    MockupManager: typeof MockupManager
  }
}

export {}
