declare global {
  interface ResponseData<T> {
    success: boolean
    message: any
    data: T
  }

  interface BundlePromotion {
    id?: number
    discount_code: string
    discount_percentage: number
    number_cart_bundle_product_limit: number | null
    is_same_campaign: boolean | null
  }
  interface StoreInfoResponse {
    storeInfo: StoreInfo
    generalSettings: GeneralSettings
  }
  interface HomePageResponse {
    best_seller: Array<Product>
    featured: Array<Product>
    new_arrivals: Array<Product>
    other: Array<{
      id: string
      name: string
      products: Array<Product>
    }>
  }
  interface PaginatorResponse<T> {
    data: T
    current_page: number
    per_page: number
    last_page: number
    total: number
    from: number
    to: number
    first_page_url: string | null
    last_page_url: string | null
    next_page_url: string | null
    prev_page_url: string | null
    links: Array<{
      url: string | null
      label: string | null
    }>
  }
  interface GetOrderResponse {
    order: Order
    payment_gateways?: Gateway[]
    payment_domain?: string
    shipping_methods?: ShippingMethod[]

    fulfillments?: Fulfillments
    timeframe?: Timeframe
  }

  interface GetIntentOrderAdditionResponse {
    clientSecret: string
    publishableKey: string
    paymentGatewayId: number
    paymentMethods?: string[]
    currencyCode?: string
  }

  interface BundleProductResponse {
    custom_personalized_options: Array<any>
    template_custom_options: Array<any>
    products: Array<Product>
    promotion: BundlePromotion
    variants: Array<Variant>
  }

  interface PersonalizeResponse {
    base_mockup: BaseMockup[]
    custom_designs: CustomDesign[]
    pb_artworks: PbArtwork[]
    three_d_mockups?: ThreeDMockupData[]
  }

}
export {}
