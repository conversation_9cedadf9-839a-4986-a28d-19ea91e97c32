# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@antfu/eslint-config@^4.13.0":
  version "4.13.0"
  dependencies:
    "@antfu/install-pkg" "^1.1.0"
    "@clack/prompts" "^0.10.1"
    "@eslint-community/eslint-plugin-eslint-comments" "^4.5.0"
    "@eslint/markdown" "^6.4.0"
    "@stylistic/eslint-plugin" "^4.2.0"
    "@typescript-eslint/eslint-plugin" "^8.32.0"
    "@typescript-eslint/parser" "^8.32.0"
    "@vitest/eslint-plugin" "^1.1.44"
    ansis "^3.17.0"
    cac "^6.7.14"
    eslint-config-flat-gitignore "^2.1.0"
    eslint-flat-config-utils "^2.0.1"
    eslint-merge-processors "^2.0.0"
    eslint-plugin-antfu "^3.1.1"
    eslint-plugin-command "^3.2.0"
    eslint-plugin-import-x "^4.11.0"
    eslint-plugin-jsdoc "^50.6.11"
    eslint-plugin-jsonc "^2.20.0"
    eslint-plugin-n "^17.17.0"
    eslint-plugin-no-only-tests "^3.3.0"
    eslint-plugin-perfectionist "^4.12.3"
    eslint-plugin-pnpm "^0.3.1"
    eslint-plugin-regexp "^2.7.0"
    eslint-plugin-toml "^0.12.0"
    eslint-plugin-unicorn "^59.0.1"
    eslint-plugin-unused-imports "^4.1.4"
    eslint-plugin-vue "^10.1.0"
    eslint-plugin-yml "^1.18.0"
    eslint-processor-vue-blocks "^2.0.0"
    globals "^16.0.0"
    jsonc-eslint-parser "^2.4.0"
    local-pkg "^1.1.1"
    parse-gitignore "^2.0.0"
    toml-eslint-parser "^0.10.0"
    vue-eslint-parser "^10.1.3"
    yaml-eslint-parser "^1.3.0"

"@antfu/install-pkg@^1.1.0":
  version "1.1.0"
  dependencies:
    package-manager-detector "^1.3.0"
    tinyexec "^1.0.1"

"@antfu/utils@^0.7.10", "@antfu/utils@^0.7.2", "@antfu/utils@^0.7.7":
  version "0.7.10"

"@apidevtools/json-schema-ref-parser@^11.6.0":
  version "11.6.4"
  dependencies:
    "@jsdevtools/ono" "^7.1.3"
    "@types/json-schema" "^7.0.15"
    js-yaml "^4.1.0"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.23.5", "@babel/code-frame@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/highlight" "^7.24.7"
    picocolors "^1.0.0"

"@babel/compat-data@^7.24.8":
  version "7.24.8"

"@babel/core@^7.23.0", "@babel/core@^7.23.7", "@babel/core@^7.24.6":
  version "7.24.8"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.24.8"
    "@babel/helper-compilation-targets" "^7.24.8"
    "@babel/helper-module-transforms" "^7.24.8"
    "@babel/helpers" "^7.24.8"
    "@babel/parser" "^7.24.8"
    "@babel/template" "^7.24.7"
    "@babel/traverse" "^7.24.8"
    "@babel/types" "^7.24.8"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/types" "^7.24.8"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^2.5.1"

"@babel/helper-annotate-as-pure@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-compilation-targets@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/compat-data" "^7.24.8"
    "@babel/helper-validator-option" "^7.24.8"
    browserslist "^4.23.1"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.24.7", "@babel/helper-create-class-features-plugin@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.8"
    "@babel/helper-optimise-call-expression" "^7.24.7"
    "@babel/helper-replace-supers" "^7.24.7"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    semver "^6.3.1"

"@babel/helper-environment-visitor@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-function-name@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-hoist-variables@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-member-expression-to-functions@^7.24.7", "@babel/helper-member-expression-to-functions@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/traverse" "^7.24.8"
    "@babel/types" "^7.24.8"

"@babel/helper-module-imports@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-module-imports@~7.22.15":
  version "7.22.15"
  dependencies:
    "@babel/types" "^7.22.15"

"@babel/helper-module-transforms@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-module-imports" "^7.24.7"
    "@babel/helper-simple-access" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/helper-validator-identifier" "^7.24.7"

"@babel/helper-optimise-call-expression@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.24.7", "@babel/helper-plugin-utils@^7.24.8":
  version "7.24.8"

"@babel/helper-replace-supers@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-member-expression-to-functions" "^7.24.7"
    "@babel/helper-optimise-call-expression" "^7.24.7"

"@babel/helper-simple-access@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-skip-transparent-expression-wrappers@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/traverse" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/helper-split-export-declaration@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/types" "^7.24.7"

"@babel/helper-string-parser@^7.24.8":
  version "7.24.8"

"@babel/helper-validator-identifier@^7.24.5", "@babel/helper-validator-identifier@^7.24.7":
  version "7.24.7"

"@babel/helper-validator-identifier@^7.25.9":
  version "7.27.1"

"@babel/helper-validator-option@^7.24.8":
  version "7.24.8"

"@babel/helpers@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/template" "^7.24.7"
    "@babel/types" "^7.24.8"

"@babel/highlight@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.7"
    chalk "^2.4.2"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/parser@^7.23.5", "@babel/parser@^7.23.9", "@babel/parser@^7.24.0", "@babel/parser@^7.24.4", "@babel/parser@^7.24.6", "@babel/parser@^7.24.7", "@babel/parser@^7.24.8":
  version "7.24.8"

"@babel/plugin-proposal-decorators@^7.23.0":
  version "7.24.7"
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.24.7"
    "@babel/helper-plugin-utils" "^7.24.7"
    "@babel/plugin-syntax-decorators" "^7.24.7"

"@babel/plugin-syntax-decorators@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-import-attributes@^7.22.5":
  version "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-jsx@^7.23.3":
  version "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-syntax-typescript@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/helper-plugin-utils" "^7.24.7"

"@babel/plugin-transform-typescript@^7.22.15", "@babel/plugin-transform-typescript@^7.24.6":
  version "7.24.8"
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.24.7"
    "@babel/helper-create-class-features-plugin" "^7.24.8"
    "@babel/helper-plugin-utils" "^7.24.8"
    "@babel/plugin-syntax-typescript" "^7.24.7"

"@babel/runtime@^7.11.2", "@babel/runtime@^7.12.5", "@babel/runtime@^7.5.5":
  version "7.25.7"
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@7.23.4":
  version "7.23.4"
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/standalone@^7.23.8":
  version "7.24.8"

"@babel/template@^7.23.9", "@babel/template@^7.24.7":
  version "7.24.7"
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/parser" "^7.24.7"
    "@babel/types" "^7.24.7"

"@babel/traverse@^7.23.9", "@babel/traverse@^7.24.7", "@babel/traverse@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/code-frame" "^7.24.7"
    "@babel/generator" "^7.24.8"
    "@babel/helper-environment-visitor" "^7.24.7"
    "@babel/helper-function-name" "^7.24.7"
    "@babel/helper-hoist-variables" "^7.24.7"
    "@babel/helper-split-export-declaration" "^7.24.7"
    "@babel/parser" "^7.24.8"
    "@babel/types" "^7.24.8"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.22.15", "@babel/types@^7.23.6", "@babel/types@^7.23.9", "@babel/types@^7.24.0", "@babel/types@^7.24.5", "@babel/types@^7.24.7", "@babel/types@^7.24.8":
  version "7.24.8"
  dependencies:
    "@babel/helper-string-parser" "^7.24.8"
    "@babel/helper-validator-identifier" "^7.24.7"
    to-fast-properties "^2.0.0"

"@clack/core@0.4.2":
  version "0.4.2"
  dependencies:
    picocolors "^1.0.0"
    sisteransi "^1.0.5"

"@clack/prompts@^0.10.1":
  version "0.10.1"
  dependencies:
    "@clack/core" "0.4.2"
    picocolors "^1.0.0"
    sisteransi "^1.0.5"

"@cloudflare/kv-asset-handler@^0.3.4":
  version "0.3.4"
  dependencies:
    mime "^3.0.0"

"@colors/colors@^1.6.0", "@colors/colors@1.6.0":
  version "1.6.0"

"@commitlint/cli@19.3.0":
  version "19.3.0"
  dependencies:
    "@commitlint/format" "^19.3.0"
    "@commitlint/lint" "^19.2.2"
    "@commitlint/load" "^19.2.0"
    "@commitlint/read" "^19.2.1"
    "@commitlint/types" "^19.0.3"
    execa "^8.0.1"
    yargs "^17.0.0"

"@commitlint/config-conventional@19.2.2":
  version "19.2.2"
  dependencies:
    "@commitlint/types" "^19.0.3"
    conventional-changelog-conventionalcommits "^7.0.2"

"@commitlint/config-validator@^19.0.3":
  version "19.0.3"
  dependencies:
    "@commitlint/types" "^19.0.3"
    ajv "^8.11.0"

"@commitlint/ensure@^19.0.3":
  version "19.0.3"
  dependencies:
    "@commitlint/types" "^19.0.3"
    lodash.camelcase "^4.3.0"
    lodash.kebabcase "^4.1.1"
    lodash.snakecase "^4.1.1"
    lodash.startcase "^4.4.0"
    lodash.upperfirst "^4.3.1"

"@commitlint/execute-rule@^19.0.0":
  version "19.0.0"

"@commitlint/format@^19.3.0":
  version "19.3.0"
  dependencies:
    "@commitlint/types" "^19.0.3"
    chalk "^5.3.0"

"@commitlint/is-ignored@^19.2.2":
  version "19.2.2"
  dependencies:
    "@commitlint/types" "^19.0.3"
    semver "^7.6.0"

"@commitlint/lint@^19.2.2":
  version "19.2.2"
  dependencies:
    "@commitlint/is-ignored" "^19.2.2"
    "@commitlint/parse" "^19.0.3"
    "@commitlint/rules" "^19.0.3"
    "@commitlint/types" "^19.0.3"

"@commitlint/load@^19.2.0":
  version "19.2.0"
  dependencies:
    "@commitlint/config-validator" "^19.0.3"
    "@commitlint/execute-rule" "^19.0.0"
    "@commitlint/resolve-extends" "^19.1.0"
    "@commitlint/types" "^19.0.3"
    chalk "^5.3.0"
    cosmiconfig "^9.0.0"
    cosmiconfig-typescript-loader "^5.0.0"
    lodash.isplainobject "^4.0.6"
    lodash.merge "^4.6.2"
    lodash.uniq "^4.5.0"

"@commitlint/message@^19.0.0":
  version "19.0.0"

"@commitlint/parse@^19.0.3":
  version "19.0.3"
  dependencies:
    "@commitlint/types" "^19.0.3"
    conventional-changelog-angular "^7.0.0"
    conventional-commits-parser "^5.0.0"

"@commitlint/read@^19.2.1":
  version "19.2.1"
  dependencies:
    "@commitlint/top-level" "^19.0.0"
    "@commitlint/types" "^19.0.3"
    execa "^8.0.1"
    git-raw-commits "^4.0.0"
    minimist "^1.2.8"

"@commitlint/resolve-extends@^19.1.0":
  version "19.1.0"
  dependencies:
    "@commitlint/config-validator" "^19.0.3"
    "@commitlint/types" "^19.0.3"
    global-directory "^4.0.1"
    import-meta-resolve "^4.0.0"
    lodash.mergewith "^4.6.2"
    resolve-from "^5.0.0"

"@commitlint/rules@^19.0.3":
  version "19.0.3"
  dependencies:
    "@commitlint/ensure" "^19.0.3"
    "@commitlint/message" "^19.0.0"
    "@commitlint/to-lines" "^19.0.0"
    "@commitlint/types" "^19.0.3"
    execa "^8.0.1"

"@commitlint/to-lines@^19.0.0":
  version "19.0.0"

"@commitlint/top-level@^19.0.0":
  version "19.0.0"
  dependencies:
    find-up "^7.0.0"

"@commitlint/types@^19.0.3":
  version "19.0.3"
  dependencies:
    "@types/conventional-commits-parser" "^5.0.0"
    chalk "^5.3.0"

"@dabh/diagnostics@^2.0.2":
  version "2.0.3"
  dependencies:
    colorspace "1.1.x"
    enabled "2.0.x"
    kuler "^2.0.0"

"@dprint/formatter@^0.3.0":
  version "0.3.0"

"@dprint/markdown@^0.17.8":
  version "0.17.8"

"@dprint/toml@^0.6.4":
  version "0.6.4"

"@es-joy/jsdoccomment@^0.50.0":
  version "0.50.0"
  dependencies:
    "@types/eslint" "^9.6.1"
    "@types/estree" "^1.0.6"
    "@typescript-eslint/types" "^8.11.0"
    comment-parser "1.4.1"
    esquery "^1.6.0"
    jsdoc-type-pratt-parser "~4.1.0"

"@es-joy/jsdoccomment@~0.46.0":
  version "0.46.0"
  dependencies:
    comment-parser "1.4.1"
    esquery "^1.6.0"
    jsdoc-type-pratt-parser "~4.0.0"

"@es-joy/jsdoccomment@~0.49.0":
  version "0.49.0"
  dependencies:
    comment-parser "1.4.1"
    esquery "^1.6.0"
    jsdoc-type-pratt-parser "~4.1.0"

"@esbuild/linux-x64@0.20.2":
  version "0.20.2"

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"

"@esbuild/linux-x64@0.23.0":
  version "0.23.0"

"@eslint-community/eslint-plugin-eslint-comments@^4.5.0":
  version "4.5.0"
  dependencies:
    escape-string-regexp "^4.0.0"
    ignore "^5.2.4"

"@eslint-community/eslint-utils@^4.1.2":
  version "4.7.0"
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.4.0":
  version "4.4.0"
  dependencies:
    eslint-visitor-keys "^3.3.0"

"@eslint-community/eslint-utils@^4.5.0":
  version "4.7.0"
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/eslint-utils@^4.5.1":
  version "4.7.0"
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.11.0", "@eslint-community/regexpp@^4.8.0", "@eslint-community/regexpp@^4.9.1":
  version "4.11.0"

"@eslint-community/regexpp@^4.12.1":
  version "4.12.1"

"@eslint/compat@^1.2.5":
  version "1.2.9"

"@eslint/config-array@^0.20.0":
  version "0.20.0"
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    debug "^4.3.1"
    minimatch "^3.1.2"

"@eslint/config-helpers@^0.2.1":
  version "0.2.2"

"@eslint/config-inspector@^0.4.8":
  version "0.4.12"
  dependencies:
    bundle-require "^5.0.0"
    cac "^6.7.14"
    chokidar "^3.6.0"
    esbuild "^0.21.5"
    fast-glob "^3.3.2"
    find-up "^7.0.0"
    get-port-please "^3.1.2"
    h3 "^1.12.0"
    minimatch "^9.0.4"
    mlly "^1.7.1"
    mrmime "^2.0.0"
    open "^10.1.0"
    picocolors "^1.0.1"
    ws "^8.17.1"

"@eslint/core@^0.10.0":
  version "0.10.0"
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/core@^0.13.0":
  version "0.13.0"
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.0.2":
  version "3.1.0"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/eslintrc@^3.3.1":
  version "3.3.1"
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^10.0.1"
    globals "^14.0.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@^9.2.0":
  version "9.7.0"

"@eslint/js@9.26.0":
  version "9.26.0"

"@eslint/markdown@^6.4.0":
  version "6.4.0"
  dependencies:
    "@eslint/core" "^0.10.0"
    "@eslint/plugin-kit" "^0.2.5"
    mdast-util-from-markdown "^2.0.2"
    mdast-util-frontmatter "^2.0.1"
    mdast-util-gfm "^3.0.0"
    micromark-extension-frontmatter "^2.0.0"
    micromark-extension-gfm "^3.0.0"

"@eslint/object-schema@^2.1.6":
  version "2.1.6"

"@eslint/plugin-kit@^0.2.5", "@eslint/plugin-kit@^0.2.7", "@eslint/plugin-kit@^0.2.8":
  version "0.2.8"
  dependencies:
    "@eslint/core" "^0.13.0"
    levn "^0.4.1"

"@fastify/busboy@^2.0.0":
  version "2.1.1"

"@fingerprintjs/fingerprintjs@3.4.0":
  version "3.4.0"
  dependencies:
    tslib "^2.4.1"

"@humanfs/core@^0.19.1":
  version "0.19.1"

"@humanfs/node@^0.16.6":
  version "0.16.6"
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"

"@humanwhocodes/retry@^0.3.0":
  version "0.3.0"

"@humanwhocodes/retry@^0.4.2":
  version "0.4.3"

"@intlify/bundle-utils@^7.4.0":
  version "7.5.1"
  dependencies:
    "@intlify/message-compiler" "^9.4.0"
    "@intlify/shared" "^9.4.0"
    acorn "^8.8.2"
    escodegen "^2.1.0"
    estree-walker "^2.0.2"
    jsonc-eslint-parser "^2.3.0"
    magic-string "^0.30.0"
    mlly "^1.2.0"
    source-map-js "^1.0.1"
    yaml-eslint-parser "^1.2.2"

"@intlify/core-base@9.13.1":
  version "9.13.1"
  dependencies:
    "@intlify/message-compiler" "9.13.1"
    "@intlify/shared" "9.13.1"

"@intlify/core@^9.8.0":
  version "9.13.1"
  dependencies:
    "@intlify/core-base" "9.13.1"
    "@intlify/shared" "9.13.1"

"@intlify/h3@^0.5.0":
  version "0.5.0"
  dependencies:
    "@intlify/core" "^9.8.0"
    "@intlify/utils" "^0.12.0"

"@intlify/message-compiler@^9.4.0", "@intlify/message-compiler@9.13.1":
  version "9.13.1"
  dependencies:
    "@intlify/shared" "9.13.1"
    source-map-js "^1.0.2"

"@intlify/shared@^9.4.0", "@intlify/shared@^9.9.0", "@intlify/shared@9.13.1":
  version "9.13.1"

"@intlify/unplugin-vue-i18n@^3.0.1":
  version "3.0.1"
  dependencies:
    "@intlify/bundle-utils" "^7.4.0"
    "@intlify/shared" "^9.4.0"
    "@rollup/pluginutils" "^5.1.0"
    "@vue/compiler-sfc" "^3.2.47"
    debug "^4.3.3"
    fast-glob "^3.2.12"
    js-yaml "^4.1.0"
    json5 "^2.2.3"
    pathe "^1.0.0"
    picocolors "^1.0.0"
    source-map-js "^1.0.2"
    unplugin "^1.1.0"

"@intlify/utils@^0.12.0":
  version "0.12.0"

"@ioredis/commands@^1.1.1":
  version "1.2.0"

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.5"
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.4.15":
  version "1.5.0"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@jsdevtools/ono@^7.1.3":
  version "7.1.3"

"@kwsites/file-exists@^1.1.1":
  version "1.1.1"
  dependencies:
    debug "^4.1.1"

"@kwsites/promise-deferred@^1.1.1":
  version "1.1.1"

"@mapbox/node-pre-gyp@^1.0.5":
  version "1.0.11"
  dependencies:
    detect-libc "^2.0.0"
    https-proxy-agent "^5.0.0"
    make-dir "^3.1.0"
    node-fetch "^2.6.7"
    nopt "^5.0.0"
    npmlog "^5.0.1"
    rimraf "^3.0.2"
    semver "^7.3.5"
    tar "^6.1.11"

"@miyaneee/rollup-plugin-json5@^1.1.2":
  version "1.2.0"
  dependencies:
    "@rollup/pluginutils" "^5.1.0"
    json5 "^2.2.3"

"@modelcontextprotocol/sdk@^1.8.0":
  version "1.11.1"
  dependencies:
    content-type "^1.0.5"
    cors "^2.8.5"
    cross-spawn "^7.0.3"
    eventsource "^3.0.2"
    express "^5.0.1"
    express-rate-limit "^7.5.0"
    pkce-challenge "^5.0.0"
    raw-body "^3.0.0"
    zod "^3.23.8"
    zod-to-json-schema "^3.24.1"

"@morev/utils@^3.7.0":
  version "3.11.1"
  dependencies:
    fast-copy "^3.0.2"
    fast-equals "^5.0.1"
    ohash "^1.1.3"
    type-fest "^4.18.3"

"@morev/vue-transitions@3.0.2":
  version "3.0.2"
  dependencies:
    "@morev/utils" "^3.7.0"
    "@nuxt/kit" "^3.10.3"

"@netlify/functions@^2.8.0":
  version "2.8.1"
  dependencies:
    "@netlify/serverless-functions-api" "1.19.1"

"@netlify/node-cookies@^0.1.0":
  version "0.1.0"

"@netlify/serverless-functions-api@1.19.1":
  version "1.19.1"
  dependencies:
    "@netlify/node-cookies" "^0.1.0"
    urlpattern-polyfill "8.0.2"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@^2.0.2", "@nodelib/fs.stat@2.0.5":
  version "2.0.5"

"@nodelib/fs.walk@^1.2.3":
  version "1.2.8"
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@nuxt/devalue@^2.0.2":
  version "2.0.2"

"@nuxt/devtools-kit@^1.3.1", "@nuxt/devtools-kit@^1.3.9", "@nuxt/devtools-kit@1.3.9":
  version "1.3.9"
  dependencies:
    "@nuxt/kit" "^3.12.2"
    "@nuxt/schema" "^3.12.3"
    execa "^7.2.0"

"@nuxt/devtools-wizard@1.3.9":
  version "1.3.9"
  dependencies:
    consola "^3.2.3"
    diff "^5.2.0"
    execa "^7.2.0"
    global-directory "^4.0.1"
    magicast "^0.3.4"
    pathe "^1.1.2"
    pkg-types "^1.1.2"
    prompts "^2.4.2"
    rc9 "^2.1.2"
    semver "^7.6.2"

"@nuxt/devtools@^1.3.7":
  version "1.3.9"
  dependencies:
    "@antfu/utils" "^0.7.10"
    "@nuxt/devtools-kit" "1.3.9"
    "@nuxt/devtools-wizard" "1.3.9"
    "@nuxt/kit" "^3.12.2"
    "@vue/devtools-core" "7.3.3"
    "@vue/devtools-kit" "7.3.3"
    birpc "^0.2.17"
    consola "^3.2.3"
    cronstrue "^2.50.0"
    destr "^2.0.3"
    error-stack-parser-es "^0.1.4"
    execa "^7.2.0"
    fast-glob "^3.3.2"
    fast-npm-meta "^0.1.1"
    flatted "^3.3.1"
    get-port-please "^3.1.2"
    hookable "^5.5.3"
    image-meta "^0.2.0"
    is-installed-globally "^1.0.0"
    launch-editor "^2.8.0"
    local-pkg "^0.5.0"
    magicast "^0.3.4"
    nypm "^0.3.9"
    ohash "^1.1.3"
    pathe "^1.1.2"
    perfect-debounce "^1.0.0"
    pkg-types "^1.1.2"
    rc9 "^2.1.2"
    scule "^1.3.0"
    semver "^7.6.2"
    simple-git "^3.25.0"
    sirv "^2.0.4"
    unimport "^3.7.2"
    vite-plugin-inspect "^0.8.4"
    vite-plugin-vue-inspector "^5.1.2"
    which "^3.0.1"
    ws "^8.17.1"

"@nuxt/eslint-config@0.3.13":
  version "0.3.13"
  dependencies:
    "@eslint/js" "^9.2.0"
    "@nuxt/eslint-plugin" "0.3.13"
    "@rushstack/eslint-patch" "^1.10.3"
    "@stylistic/eslint-plugin" "^2.1.0"
    "@typescript-eslint/eslint-plugin" "^7.9.0"
    "@typescript-eslint/parser" "^7.9.0"
    eslint-config-flat-gitignore "^0.1.5"
    eslint-flat-config-utils "^0.2.5"
    eslint-plugin-import-x "^0.5.0"
    eslint-plugin-jsdoc "^48.2.5"
    eslint-plugin-regexp "^2.5.0"
    eslint-plugin-unicorn "^53.0.0"
    eslint-plugin-vue "^9.26.0"
    globals "^15.2.0"
    pathe "^1.1.2"
    tslib "^2.6.2"
    vue-eslint-parser "^9.4.2"

"@nuxt/eslint-plugin@0.3.13":
  version "0.3.13"
  dependencies:
    "@typescript-eslint/types" "^7.9.0"
    "@typescript-eslint/utils" "^7.9.0"

"@nuxt/eslint@^0.3.13":
  version "0.3.13"
  dependencies:
    "@eslint/config-inspector" "^0.4.8"
    "@nuxt/devtools-kit" "^1.3.1"
    "@nuxt/eslint-config" "0.3.13"
    "@nuxt/eslint-plugin" "0.3.13"
    "@nuxt/kit" "^3.11.2"
    chokidar "^3.6.0"
    eslint-flat-config-utils "^0.2.4"
    eslint-typegen "^0.2.4"
    find-up "^7.0.0"
    get-port-please "^3.1.2"
    mlly "^1.7.0"
    pathe "^1.1.2"
    unimport "^3.7.1"

"@nuxt/kit@^3.10.3", "@nuxt/kit@^3.11.2", "@nuxt/kit@^3.12.2", "@nuxt/kit@^3.12.3", "@nuxt/kit@^3.5.0", "@nuxt/kit@3.12.3":
  version "3.12.3"
  dependencies:
    "@nuxt/schema" "3.12.3"
    c12 "^1.11.1"
    consola "^3.2.3"
    defu "^6.1.4"
    destr "^2.0.3"
    globby "^14.0.2"
    hash-sum "^2.0.0"
    ignore "^5.3.1"
    jiti "^1.21.6"
    klona "^2.0.6"
    knitwork "^1.1.0"
    mlly "^1.7.1"
    pathe "^1.1.2"
    pkg-types "^1.1.2"
    scule "^1.3.0"
    semver "^7.6.2"
    ufo "^1.5.3"
    unctx "^2.3.1"
    unimport "^3.7.2"
    untyped "^1.4.2"

"@nuxt/kit@npm:@nuxt/kit-edge@3.8.0-28284309.b3d3d7f4":
  version "3.8.0-28284309.b3d3d7f4"
  dependencies:
    "@nuxt/schema" "npm:@nuxt/schema-edge@3.8.0-28284309.b3d3d7f4"
    c12 "^1.4.2"
    consola "^3.2.3"
    defu "^6.1.2"
    globby "^13.2.2"
    hash-sum "^2.0.0"
    ignore "^5.2.4"
    jiti "^1.20.0"
    knitwork "^1.0.0"
    mlly "^1.4.2"
    pathe "^1.1.1"
    pkg-types "^1.0.3"
    scule "^1.0.0"
    semver "^7.5.4"
    ufo "^1.3.1"
    unctx "^2.3.1"
    unimport "^3.4.0"
    untyped "^1.4.0"

"@nuxt/kit@npm:@nuxt/kit-edge@latest":
  version "3.8.0-28284309.b3d3d7f4"
  resolved "https://registry.npmjs.org/@nuxt/kit-edge/-/kit-edge-3.8.0-28284309.b3d3d7f4.tgz"
  integrity sha512-yUUBNl+VF/+q0MJEKYAOXv72wvXzqqRnYW0beQcpXIhVZI8K7buoLIaZeAAsGtbMoIjDGRVoRaRq1paTcYRHvA==
  dependencies:
    "@nuxt/schema" "npm:@nuxt/schema-edge@3.8.0-28284309.b3d3d7f4"
    c12 "^1.4.2"
    consola "^3.2.3"
    defu "^6.1.2"
    globby "^13.2.2"
    hash-sum "^2.0.0"
    ignore "^5.2.4"
    jiti "^1.20.0"
    knitwork "^1.0.0"
    mlly "^1.4.2"
    pathe "^1.1.1"
    pkg-types "^1.0.3"
    scule "^1.0.0"
    semver "^7.5.4"
    ufo "^1.3.1"
    unctx "^2.3.1"
    unimport "^3.4.0"
    untyped "^1.4.0"

"@nuxt/schema@^3.12.3", "@nuxt/schema@3.12.3":
  version "3.12.3"
  dependencies:
    compatx "^0.1.8"
    consola "^3.2.3"
    defu "^6.1.4"
    hookable "^5.5.3"
    pathe "^1.1.2"
    pkg-types "^1.1.2"
    scule "^1.3.0"
    std-env "^3.7.0"
    ufo "^1.5.3"
    uncrypto "^0.1.3"
    unimport "^3.7.2"
    untyped "^1.4.2"

"@nuxt/schema@npm:@nuxt/schema-edge@3.8.0-28284309.b3d3d7f4":
  version "3.8.0-28284309.b3d3d7f4"
  dependencies:
    "@nuxt/ui-templates" "^1.3.1"
    consola "^3.2.3"
    defu "^6.1.2"
    hookable "^5.5.3"
    pathe "^1.1.1"
    pkg-types "^1.0.3"
    postcss-import-resolver "^2.0.0"
    std-env "^3.4.3"
    ufo "^1.3.1"
    unimport "^3.4.0"
    untyped "^1.4.0"

"@nuxt/telemetry@^2.5.4":
  version "2.5.4"
  dependencies:
    "@nuxt/kit" "^3.11.2"
    ci-info "^4.0.0"
    consola "^3.2.3"
    create-require "^1.1.1"
    defu "^6.1.4"
    destr "^2.0.3"
    dotenv "^16.4.5"
    git-url-parse "^14.0.0"
    is-docker "^3.0.0"
    jiti "^1.21.0"
    mri "^1.2.0"
    nanoid "^5.0.7"
    ofetch "^1.3.4"
    parse-git-config "^3.0.0"
    pathe "^1.1.2"
    rc9 "^2.1.2"
    std-env "^3.7.0"

"@nuxt/test-utils-edge@3.8.0-28284309.b3d3d7f4":
  version "3.8.0-28284309.b3d3d7f4"
  dependencies:
    "@nuxt/kit" "npm:@nuxt/kit-edge@3.8.0-28284309.b3d3d7f4"
    "@nuxt/schema" "npm:@nuxt/schema-edge@3.8.0-28284309.b3d3d7f4"
    consola "^3.2.3"
    defu "^6.1.2"
    execa "^7.2.0"
    get-port-please "^3.1.1"
    ofetch "^1.3.3"
    pathe "^1.1.1"
    ufo "^1.3.1"

"@nuxt/ui-templates@^1.3.1":
  version "1.3.4"

"@nuxt/vite-builder@3.12.3":
  version "3.12.3"
  dependencies:
    "@nuxt/kit" "3.12.3"
    "@rollup/plugin-replace" "^5.0.7"
    "@vitejs/plugin-vue" "^5.0.5"
    "@vitejs/plugin-vue-jsx" "^4.0.0"
    autoprefixer "^10.4.19"
    clear "^0.1.0"
    consola "^3.2.3"
    cssnano "^7.0.3"
    defu "^6.1.4"
    esbuild "^0.23.0"
    escape-string-regexp "^5.0.0"
    estree-walker "^3.0.3"
    externality "^1.0.2"
    get-port-please "^3.1.2"
    h3 "^1.12.0"
    knitwork "^1.1.0"
    magic-string "^0.30.10"
    mlly "^1.7.1"
    ohash "^1.1.3"
    pathe "^1.1.2"
    perfect-debounce "^1.0.0"
    pkg-types "^1.1.2"
    postcss "^8.4.39"
    rollup-plugin-visualizer "^5.12.0"
    std-env "^3.7.0"
    strip-literal "^2.1.0"
    ufo "^1.5.3"
    unenv "^1.9.0"
    unplugin "^1.11.0"
    vite "^5.3.2"
    vite-node "^1.6.0"
    vite-plugin-checker "^0.7.0"
    vue-bundle-renderer "^2.1.0"

"@nuxtjs/color-mode@3.4.2":
  version "3.4.2"
  dependencies:
    "@nuxt/kit" "^3.12.2"
    pathe "^1.1.2"
    pkg-types "^1.1.1"
    semver "^7.6.2"

"@nuxtjs/i18n@8.3.1":
  version "8.3.1"
  dependencies:
    "@intlify/h3" "^0.5.0"
    "@intlify/shared" "^9.9.0"
    "@intlify/unplugin-vue-i18n" "^3.0.1"
    "@intlify/utils" "^0.12.0"
    "@miyaneee/rollup-plugin-json5" "^1.1.2"
    "@nuxt/kit" "^3.10.3"
    "@rollup/plugin-yaml" "^4.1.2"
    "@vue/compiler-sfc" "^3.3.4"
    debug "^4.3.4"
    defu "^6.1.2"
    estree-walker "^3.0.3"
    is-https "^4.0.0"
    knitwork "^1.0.0"
    magic-string "^0.30.4"
    mlly "^1.4.2"
    pathe "^1.1.1"
    scule "^1.1.1"
    sucrase "^3.34.0"
    ufo "^1.3.1"
    unplugin "^1.5.0"
    vue-i18n "^9.9.0"
    vue-router "^4.2.5"

"@parcel/watcher-linux-x64-glibc@2.4.1":
  version "2.4.1"

"@parcel/watcher-wasm@^2.4.1":
  version "2.4.1"
  dependencies:
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    napi-wasm "^1.1.0"

"@parcel/watcher@^2.4.1":
  version "2.4.1"
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.4.1"
    "@parcel/watcher-darwin-arm64" "2.4.1"
    "@parcel/watcher-darwin-x64" "2.4.1"
    "@parcel/watcher-freebsd-x64" "2.4.1"
    "@parcel/watcher-linux-arm-glibc" "2.4.1"
    "@parcel/watcher-linux-arm64-glibc" "2.4.1"
    "@parcel/watcher-linux-arm64-musl" "2.4.1"
    "@parcel/watcher-linux-x64-glibc" "2.4.1"
    "@parcel/watcher-linux-x64-musl" "2.4.1"
    "@parcel/watcher-win32-arm64" "2.4.1"
    "@parcel/watcher-win32-ia32" "2.4.1"
    "@parcel/watcher-win32-x64" "2.4.1"

"@pinia-plugin-persistedstate/nuxt@1.2.1":
  version "1.2.1"
  dependencies:
    "@nuxt/kit" "^3.12.2"
    defu "^6.1.4"
    pinia-plugin-persistedstate ">=3.2.1"

"@pinia/nuxt@0.5.1":
  version "0.5.1"
  dependencies:
    "@nuxt/kit" "^3.5.0"
    pinia ">=2.1.7"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"

"@pkgr/core@^0.1.0":
  version "0.1.1"

"@pkgr/core@^0.2.0":
  version "0.2.4"

"@polka/url@^1.0.0-next.20", "@polka/url@^1.0.0-next.24":
  version "1.0.0-next.25"

"@rollup/plugin-alias@^5.1.0":
  version "5.1.0"
  dependencies:
    slash "^4.0.0"

"@rollup/plugin-commonjs@^25.0.8":
  version "25.0.8"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    commondir "^1.0.1"
    estree-walker "^2.0.2"
    glob "^8.0.3"
    is-reference "1.2.1"
    magic-string "^0.30.3"

"@rollup/plugin-inject@^5.0.5":
  version "5.0.5"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    estree-walker "^2.0.2"
    magic-string "^0.30.3"

"@rollup/plugin-json@^6.1.0":
  version "6.1.0"
  dependencies:
    "@rollup/pluginutils" "^5.1.0"

"@rollup/plugin-node-resolve@^15.2.3":
  version "15.2.3"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    "@types/resolve" "1.20.2"
    deepmerge "^4.2.2"
    is-builtin-module "^3.2.1"
    is-module "^1.0.0"
    resolve "^1.22.1"

"@rollup/plugin-replace@^5.0.7":
  version "5.0.7"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    magic-string "^0.30.3"

"@rollup/plugin-terser@^0.4.4":
  version "0.4.4"
  dependencies:
    serialize-javascript "^6.0.1"
    smob "^1.0.0"
    terser "^5.17.4"

"@rollup/plugin-yaml@^4.1.2":
  version "4.1.2"
  dependencies:
    "@rollup/pluginutils" "^5.0.1"
    js-yaml "^4.1.0"
    tosource "^2.0.0-alpha.3"

"@rollup/pluginutils@^4.0.0":
  version "4.2.1"
  dependencies:
    estree-walker "^2.0.1"
    picomatch "^2.2.2"

"@rollup/pluginutils@^5.0.1", "@rollup/pluginutils@^5.1.0":
  version "5.1.0"
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^2.3.1"

"@rollup/rollup-linux-x64-gnu@4.18.1":
  version "4.18.1"

"@rushstack/eslint-patch@^1.10.3":
  version "1.10.3"

"@sentry-internal/feedback@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry/core" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sentry-internal/replay-canvas@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry/core" "7.118.0"
    "@sentry/replay" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sentry-internal/tracing@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry/core" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sentry/browser@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry-internal/feedback" "7.118.0"
    "@sentry-internal/replay-canvas" "7.118.0"
    "@sentry-internal/tracing" "7.118.0"
    "@sentry/core" "7.118.0"
    "@sentry/integrations" "7.118.0"
    "@sentry/replay" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sentry/core@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sentry/integrations@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry/core" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"
    localforage "^1.8.1"

"@sentry/replay@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry-internal/tracing" "7.118.0"
    "@sentry/core" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sentry/types@7.118.0":
  version "7.118.0"

"@sentry/utils@7.118.0":
  version "7.118.0"
  dependencies:
    "@sentry/types" "7.118.0"

"@sentry/vue@^7.64.0":
  version "7.118.0"
  dependencies:
    "@sentry/browser" "7.118.0"
    "@sentry/core" "7.118.0"
    "@sentry/types" "7.118.0"
    "@sentry/utils" "7.118.0"

"@sindresorhus/merge-streams@^2.1.0":
  version "2.3.0"

"@splidejs/splide-extension-grid@^0.4.1":
  version "0.4.1"

"@splidejs/splide@^4.1.3":
  version "4.1.4"

"@splidejs/vue-splide@^0.6.12":
  version "0.6.12"
  dependencies:
    "@splidejs/splide" "^4.1.3"

"@stylistic/eslint-plugin-js@^2.3.0", "@stylistic/eslint-plugin-js@2.3.0":
  version "2.3.0"
  dependencies:
    "@types/eslint" "^8.56.10"
    acorn "^8.11.3"
    eslint-visitor-keys "^4.0.0"
    espree "^10.0.1"

"@stylistic/eslint-plugin-jsx@2.3.0":
  version "2.3.0"
  dependencies:
    "@stylistic/eslint-plugin-js" "^2.3.0"
    "@types/eslint" "^8.56.10"
    estraverse "^5.3.0"
    picomatch "^4.0.2"

"@stylistic/eslint-plugin-plus@2.3.0":
  version "2.3.0"
  dependencies:
    "@types/eslint" "^8.56.10"
    "@typescript-eslint/utils" "^7.12.0"

"@stylistic/eslint-plugin-ts@2.3.0":
  version "2.3.0"
  dependencies:
    "@stylistic/eslint-plugin-js" "2.3.0"
    "@types/eslint" "^8.56.10"
    "@typescript-eslint/utils" "^7.12.0"

"@stylistic/eslint-plugin@^2.1.0":
  version "2.3.0"
  dependencies:
    "@stylistic/eslint-plugin-js" "2.3.0"
    "@stylistic/eslint-plugin-jsx" "2.3.0"
    "@stylistic/eslint-plugin-plus" "2.3.0"
    "@stylistic/eslint-plugin-ts" "2.3.0"
    "@types/eslint" "^8.56.10"

"@stylistic/eslint-plugin@^4.2.0":
  version "4.2.0"
  dependencies:
    "@typescript-eslint/utils" "^8.23.0"
    eslint-visitor-keys "^4.2.0"
    espree "^10.3.0"
    estraverse "^5.3.0"
    picomatch "^4.0.2"

"@trysound/sax@0.2.0":
  version "0.2.0"

"@types/conventional-commits-parser@^5.0.0":
  version "5.0.0"
  dependencies:
    "@types/node" "*"

"@types/debug@^4.0.0":
  version "4.1.12"
  dependencies:
    "@types/ms" "*"

"@types/dompurify@^3.0.5":
  version "3.0.5"
  dependencies:
    "@types/trusted-types" "*"

"@types/eslint@^8.56.10":
  version "8.56.10"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/eslint@^9.6.1":
  version "9.6.1"
  dependencies:
    "@types/estree" "*"
    "@types/json-schema" "*"

"@types/estree@*", "@types/estree@^1.0.0", "@types/estree@1.0.5":
  version "1.0.5"

"@types/estree@^1.0.6":
  version "1.0.7"

"@types/http-proxy@^1.17.14":
  version "1.17.14"
  dependencies:
    "@types/node" "*"

"@types/json-schema@*", "@types/json-schema@^7.0.15":
  version "7.0.15"

"@types/mdast@^4.0.0":
  version "4.0.4"
  dependencies:
    "@types/unist" "*"

"@types/ms@*":
  version "2.1.0"

"@types/node@*":
  version "20.14.10"
  dependencies:
    undici-types "~5.26.4"

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"

"@types/resolve@1.20.2":
  version "1.20.2"

"@types/sanitize-html@^2.11.0":
  version "2.11.0"
  dependencies:
    htmlparser2 "^8.0.0"

"@types/triple-beam@^1.3.2":
  version "1.3.5"

"@types/trusted-types@*":
  version "2.0.7"

"@types/ua-parser-js@^0.7.39":
  version "0.7.39"

"@types/unist@*", "@types/unist@^3.0.0":
  version "3.0.3"

"@types/uuid@^9.0.1":
  version "9.0.8"

"@types/vue-tel-input@2.1.2":
  version "2.1.2"
  dependencies:
    vue "^2.0.0"

"@typescript-eslint/eslint-plugin@^7.16.1":
  version "7.16.1"
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "7.16.1"
    "@typescript-eslint/type-utils" "7.16.1"
    "@typescript-eslint/utils" "7.16.1"
    "@typescript-eslint/visitor-keys" "7.16.1"
    graphemer "^1.4.0"
    ignore "^5.3.1"
    natural-compare "^1.4.0"
    ts-api-utils "^1.3.0"

"@typescript-eslint/eslint-plugin@^7.9.0":
  version "7.16.1"
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "7.16.1"
    "@typescript-eslint/type-utils" "7.16.1"
    "@typescript-eslint/utils" "7.16.1"
    "@typescript-eslint/visitor-keys" "7.16.1"
    graphemer "^1.4.0"
    ignore "^5.3.1"
    natural-compare "^1.4.0"
    ts-api-utils "^1.3.0"

"@typescript-eslint/eslint-plugin@^8.32.0":
  version "8.32.0"
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.32.0"
    "@typescript-eslint/type-utils" "8.32.0"
    "@typescript-eslint/utils" "8.32.0"
    "@typescript-eslint/visitor-keys" "8.32.0"
    graphemer "^1.4.0"
    ignore "^5.3.1"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@^7.9.0":
  version "7.16.1"
  dependencies:
    "@typescript-eslint/scope-manager" "7.16.1"
    "@typescript-eslint/types" "7.16.1"
    "@typescript-eslint/typescript-estree" "7.16.1"
    "@typescript-eslint/visitor-keys" "7.16.1"
    debug "^4.3.4"

"@typescript-eslint/parser@^8.32.0":
  version "8.32.0"
  dependencies:
    "@typescript-eslint/scope-manager" "8.32.0"
    "@typescript-eslint/types" "8.32.0"
    "@typescript-eslint/typescript-estree" "8.32.0"
    "@typescript-eslint/visitor-keys" "8.32.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@7.16.1":
  version "7.16.1"
  dependencies:
    "@typescript-eslint/types" "7.16.1"
    "@typescript-eslint/visitor-keys" "7.16.1"

"@typescript-eslint/scope-manager@8.32.0":
  version "8.32.0"
  dependencies:
    "@typescript-eslint/types" "8.32.0"
    "@typescript-eslint/visitor-keys" "8.32.0"

"@typescript-eslint/type-utils@7.16.1":
  version "7.16.1"
  dependencies:
    "@typescript-eslint/typescript-estree" "7.16.1"
    "@typescript-eslint/utils" "7.16.1"
    debug "^4.3.4"
    ts-api-utils "^1.3.0"

"@typescript-eslint/type-utils@8.32.0":
  version "8.32.0"
  dependencies:
    "@typescript-eslint/typescript-estree" "8.32.0"
    "@typescript-eslint/utils" "8.32.0"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@^7.9.0":
  version "7.16.1"

"@typescript-eslint/types@^8.11.0", "@typescript-eslint/types@^8.31.0", "@typescript-eslint/types@8.32.0":
  version "8.32.0"

"@typescript-eslint/types@7.16.1":
  version "7.16.1"

"@typescript-eslint/typescript-estree@7.16.1":
  version "7.16.1"
  dependencies:
    "@typescript-eslint/types" "7.16.1"
    "@typescript-eslint/visitor-keys" "7.16.1"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^1.3.0"

"@typescript-eslint/typescript-estree@8.32.0":
  version "8.32.0"
  dependencies:
    "@typescript-eslint/types" "8.32.0"
    "@typescript-eslint/visitor-keys" "8.32.0"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@^7.12.0", "@typescript-eslint/utils@^7.4.0", "@typescript-eslint/utils@^7.9.0", "@typescript-eslint/utils@7.16.1":
  version "7.16.1"
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    "@typescript-eslint/scope-manager" "7.16.1"
    "@typescript-eslint/types" "7.16.1"
    "@typescript-eslint/typescript-estree" "7.16.1"

"@typescript-eslint/utils@^8.23.0":
  version "8.32.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.32.0"
    "@typescript-eslint/types" "8.32.0"
    "@typescript-eslint/typescript-estree" "8.32.0"

"@typescript-eslint/utils@^8.31.0":
  version "8.32.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.32.0"
    "@typescript-eslint/types" "8.32.0"
    "@typescript-eslint/typescript-estree" "8.32.0"

"@typescript-eslint/utils@8.32.0":
  version "8.32.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.32.0"
    "@typescript-eslint/types" "8.32.0"
    "@typescript-eslint/typescript-estree" "8.32.0"

"@typescript-eslint/visitor-keys@7.16.1":
  version "7.16.1"
  dependencies:
    "@typescript-eslint/types" "7.16.1"
    eslint-visitor-keys "^3.4.3"

"@typescript-eslint/visitor-keys@8.32.0":
  version "8.32.0"
  dependencies:
    "@typescript-eslint/types" "8.32.0"
    eslint-visitor-keys "^4.2.0"

"@unhead/dom@^1.9.14", "@unhead/dom@1.9.15":
  version "1.9.15"
  dependencies:
    "@unhead/schema" "1.9.15"
    "@unhead/shared" "1.9.15"

"@unhead/schema-org@^1.9.16":
  version "1.9.16"
  dependencies:
    ufo "^1.5.3"

"@unhead/schema@1.9.15":
  version "1.9.15"
  dependencies:
    hookable "^5.5.3"
    zhead "^2.2.4"

"@unhead/shared@1.9.15":
  version "1.9.15"
  dependencies:
    "@unhead/schema" "1.9.15"

"@unhead/ssr@^1.9.14":
  version "1.9.15"
  dependencies:
    "@unhead/schema" "1.9.15"
    "@unhead/shared" "1.9.15"

"@unhead/vue@^1.9.14":
  version "1.9.15"
  dependencies:
    "@unhead/schema" "1.9.15"
    "@unhead/shared" "1.9.15"
    hookable "^5.5.3"
    unhead "1.9.15"

"@unrs/resolver-binding-linux-x64-gnu@1.7.2":
  version "1.7.2"

"@vercel/nft@^0.26.5":
  version "0.26.5"
  dependencies:
    "@mapbox/node-pre-gyp" "^1.0.5"
    "@rollup/pluginutils" "^4.0.0"
    acorn "^8.6.0"
    acorn-import-attributes "^1.9.2"
    async-sema "^3.1.1"
    bindings "^1.4.0"
    estree-walker "2.0.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    micromatch "^4.0.2"
    node-gyp-build "^4.2.2"
    resolve-from "^5.0.0"

"@videojs-player/vue@^1.0.0":
  version "1.0.0"

"@videojs/http-streaming@3.13.3":
  version "3.13.3"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "4.0.0"
    aes-decrypter "4.0.1"
    global "^4.4.0"
    m3u8-parser "^7.1.0"
    mpd-parser "^1.3.0"
    mux.js "7.0.3"
    video.js "^7 || ^8"

"@videojs/vhs-utils@^3.0.5":
  version "3.0.5"
  dependencies:
    "@babel/runtime" "^7.12.5"
    global "^4.4.0"
    url-toolkit "^2.2.1"

"@videojs/vhs-utils@^4.0.0", "@videojs/vhs-utils@^4.1.1":
  version "4.1.1"
  dependencies:
    "@babel/runtime" "^7.12.5"
    global "^4.4.0"

"@videojs/vhs-utils@4.0.0":
  version "4.0.0"
  dependencies:
    "@babel/runtime" "^7.12.5"
    global "^4.4.0"
    url-toolkit "^2.2.1"

"@videojs/xhr@2.7.0":
  version "2.7.0"
  dependencies:
    "@babel/runtime" "^7.5.5"
    global "~4.4.0"
    is-function "^1.0.1"

"@vitejs/plugin-vue-jsx@^4.0.0":
  version "4.0.0"
  dependencies:
    "@babel/core" "^7.24.6"
    "@babel/plugin-transform-typescript" "^7.24.6"
    "@vue/babel-plugin-jsx" "^1.2.2"

"@vitejs/plugin-vue@^5.0.5":
  version "5.0.5"

"@vitest/eslint-plugin@^1.1.44":
  version "1.1.44"

"@vue-macros/common@^1.10.4":
  version "1.10.4"
  dependencies:
    "@babel/types" "^7.24.5"
    "@rollup/pluginutils" "^5.1.0"
    "@vue/compiler-sfc" "^3.4.27"
    ast-kit "^0.12.1"
    local-pkg "^0.5.0"
    magic-string-ast "^0.6.0"

"@vue/babel-helper-vue-transform-on@1.2.2":
  version "1.2.2"

"@vue/babel-plugin-jsx@^1.1.5", "@vue/babel-plugin-jsx@^1.2.2":
  version "1.2.2"
  dependencies:
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/plugin-syntax-jsx" "^7.23.3"
    "@babel/template" "^7.23.9"
    "@babel/traverse" "^7.23.9"
    "@babel/types" "^7.23.9"
    "@vue/babel-helper-vue-transform-on" "1.2.2"
    "@vue/babel-plugin-resolve-type" "1.2.2"
    camelcase "^6.3.0"
    html-tags "^3.3.1"
    svg-tags "^1.0.0"

"@vue/babel-plugin-resolve-type@1.2.2":
  version "1.2.2"
  dependencies:
    "@babel/code-frame" "^7.23.5"
    "@babel/helper-module-imports" "~7.22.15"
    "@babel/helper-plugin-utils" "^7.22.5"
    "@babel/parser" "^7.23.9"
    "@vue/compiler-sfc" "^3.4.15"

"@vue/compiler-core@3.4.31":
  version "3.4.31"
  dependencies:
    "@babel/parser" "^7.24.7"
    "@vue/shared" "3.4.31"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.0"

"@vue/compiler-dom@^3.3.4", "@vue/compiler-dom@3.4.31":
  version "3.4.31"
  dependencies:
    "@vue/compiler-core" "3.4.31"
    "@vue/shared" "3.4.31"

"@vue/compiler-sfc@^3.2.47", "@vue/compiler-sfc@^3.3.4", "@vue/compiler-sfc@^3.4.15", "@vue/compiler-sfc@^3.4.27", "@vue/compiler-sfc@3.4.31":
  version "3.4.31"
  dependencies:
    "@babel/parser" "^7.24.7"
    "@vue/compiler-core" "3.4.31"
    "@vue/compiler-dom" "3.4.31"
    "@vue/compiler-ssr" "3.4.31"
    "@vue/shared" "3.4.31"
    estree-walker "^2.0.2"
    magic-string "^0.30.10"
    postcss "^8.4.38"
    source-map-js "^1.2.0"

"@vue/compiler-sfc@2.7.16":
  version "2.7.16"
  dependencies:
    "@babel/parser" "^7.23.5"
    postcss "^8.4.14"
    source-map "^0.6.1"
  optionalDependencies:
    prettier "^1.18.2 || ^2.0.0"

"@vue/compiler-ssr@3.4.31":
  version "3.4.31"
  dependencies:
    "@vue/compiler-dom" "3.4.31"
    "@vue/shared" "3.4.31"

"@vue/devtools-api@^6.5.0", "@vue/devtools-api@^6.5.1":
  version "6.6.3"

"@vue/devtools-core@7.3.3":
  version "7.3.3"
  dependencies:
    "@vue/devtools-kit" "^7.3.3"
    "@vue/devtools-shared" "^7.3.3"
    mitt "^3.0.1"
    nanoid "^3.3.4"
    pathe "^1.1.2"
    vite-hot-client "^0.2.3"

"@vue/devtools-kit@^7.3.3":
  version "7.3.5"
  dependencies:
    "@vue/devtools-shared" "^7.3.5"
    birpc "^0.2.17"
    hookable "^5.5.3"
    mitt "^3.0.1"
    perfect-debounce "^1.0.0"
    speakingurl "^14.0.1"
    superjson "^2.2.1"

"@vue/devtools-kit@7.3.3":
  version "7.3.3"
  dependencies:
    "@vue/devtools-shared" "^7.3.3"
    birpc "^0.2.17"
    hookable "^5.5.3"
    mitt "^3.0.1"
    perfect-debounce "^1.0.0"
    speakingurl "^14.0.1"
    superjson "^2.2.1"

"@vue/devtools-shared@^7.3.3", "@vue/devtools-shared@^7.3.5":
  version "7.3.5"
  dependencies:
    rfdc "^1.4.1"

"@vue/reactivity@3.4.31":
  version "3.4.31"
  dependencies:
    "@vue/shared" "3.4.31"

"@vue/runtime-core@3.4.31":
  version "3.4.31"
  dependencies:
    "@vue/reactivity" "3.4.31"
    "@vue/shared" "3.4.31"

"@vue/runtime-dom@3.4.31":
  version "3.4.31"
  dependencies:
    "@vue/reactivity" "3.4.31"
    "@vue/runtime-core" "3.4.31"
    "@vue/shared" "3.4.31"
    csstype "^3.1.3"

"@vue/server-renderer@3.4.31":
  version "3.4.31"
  dependencies:
    "@vue/compiler-ssr" "3.4.31"
    "@vue/shared" "3.4.31"

"@vue/shared@^3.4.31", "@vue/shared@3.4.31":
  version "3.4.31"

"@windicss/config@^1.9.3", "@windicss/config@1.9.3":
  version "1.9.3"
  dependencies:
    debug "^4.3.4"
    jiti "^1.18.2"
    windicss "^3.5.6"

"@windicss/plugin-utils@^1.1.1", "@windicss/plugin-utils@^1.9.1", "@windicss/plugin-utils@^1.9.3", "@windicss/plugin-utils@1.9.3":
  version "1.9.3"
  dependencies:
    "@antfu/utils" "^0.7.2"
    "@windicss/config" "1.9.3"
    debug "^4.3.4"
    fast-glob "^3.2.12"
    magic-string "^0.30.0"
    micromatch "^4.0.5"
    windicss "^3.5.6"

"@xmldom/xmldom@^0.8.3":
  version "0.8.10"

abbrev@1:
  version "1.1.1"

abort-controller@^3.0.0:
  version "3.0.0"
  dependencies:
    event-target-shim "^5.0.0"

accepts@^2.0.0:
  version "2.0.0"
  dependencies:
    mime-types "^3.0.0"
    negotiator "^1.0.0"

acorn-import-attributes@^1.9.2:
  version "1.9.5"

acorn-jsx@^5.3.2:
  version "5.3.2"

acorn@^8.11.3, acorn@^8.12.0, acorn@^8.12.1, acorn@^8.5.0, acorn@^8.6.0, acorn@^8.8.2, acorn@^8.9.0:
  version "8.12.1"

acorn@^8.14.0:
  version "8.14.1"

acorn@8.12.0:
  version "8.12.0"

aes-decrypter@^4.0.1:
  version "4.0.2"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^4.1.1"
    global "^4.4.0"
    pkcs7 "^1.0.4"

aes-decrypter@4.0.1:
  version "4.0.1"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^3.0.5"
    global "^4.4.0"
    pkcs7 "^1.0.4"

agent-base@6:
  version "6.0.2"
  dependencies:
    debug "4"

ajv@^6.12.4:
  version "6.12.6"
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.11.0:
  version "8.17.1"
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ansi-colors@^4.1.3:
  version "4.1.3"

ansi-escapes@^4.3.0:
  version "4.3.2"
  dependencies:
    type-fest "^0.21.3"

ansi-escapes@^6.2.0:
  version "6.2.1"

ansi-regex@^5.0.1:
  version "5.0.1"

ansi-regex@^6.0.1:
  version "6.0.1"

ansi-styles@^3.2.1:
  version "3.2.1"
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^4.1.0:
  version "4.3.0"
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.0.0, ansi-styles@^6.1.0, ansi-styles@^6.2.1:
  version "6.2.1"

ansis@^3.17.0:
  version "3.17.0"

any-promise@^1.0.0:
  version "1.3.0"

anymatch@^3.1.3, anymatch@~3.1.2:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

"aproba@^1.0.3 || ^2.0.0":
  version "2.0.0"

archiver-utils@^5.0.0, archiver-utils@^5.0.2:
  version "5.0.2"
  dependencies:
    glob "^10.0.0"
    graceful-fs "^4.2.0"
    is-stream "^2.0.1"
    lazystream "^1.0.0"
    lodash "^4.17.15"
    normalize-path "^3.0.0"
    readable-stream "^4.0.0"

archiver@^7.0.1:
  version "7.0.1"
  dependencies:
    archiver-utils "^5.0.2"
    async "^3.2.4"
    buffer-crc32 "^1.0.0"
    readable-stream "^4.0.0"
    readdir-glob "^1.1.2"
    tar-stream "^3.0.0"
    zip-stream "^6.0.1"

are-docs-informative@^0.0.2:
  version "0.0.2"

are-we-there-yet@^2.0.0:
  version "2.0.0"
  dependencies:
    delegates "^1.0.0"
    readable-stream "^3.6.0"

argparse@^2.0.1:
  version "2.0.1"

array-ify@^1.0.0:
  version "1.0.0"

array-union@^2.1.0:
  version "2.1.0"

ast-kit@^0.12.1:
  version "0.12.2"
  dependencies:
    "@babel/parser" "^7.24.6"
    pathe "^1.1.2"

ast-walker-scope@^0.6.1:
  version "0.6.1"
  dependencies:
    "@babel/parser" "^7.24.0"
    ast-kit "^0.12.1"

async-sema@^3.1.1:
  version "3.1.1"

async@^3.2.3, async@^3.2.4:
  version "3.2.5"

asynckit@^0.4.0:
  version "0.4.0"

autoprefixer@^10.4.19:
  version "10.4.19"
  dependencies:
    browserslist "^4.23.0"
    caniuse-lite "^1.0.30001599"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.0.0"
    postcss-value-parser "^4.2.0"

b4a@^1.6.4:
  version "1.6.6"

balanced-match@^1.0.0:
  version "1.0.2"

bare-events@^2.2.0:
  version "2.4.2"

base64-js@^1.3.1:
  version "1.5.1"

big.js@^5.2.2:
  version "5.2.2"

binary-extensions@^2.0.0:
  version "2.3.0"

bindings@^1.4.0:
  version "1.5.0"
  dependencies:
    file-uri-to-path "1.0.0"

birpc@^0.2.17:
  version "0.2.17"

body-parser@^2.2.0:
  version "2.2.0"
  dependencies:
    bytes "^3.1.2"
    content-type "^1.0.5"
    debug "^4.4.0"
    http-errors "^2.0.0"
    iconv-lite "^0.6.3"
    on-finished "^2.4.1"
    qs "^6.14.0"
    raw-body "^3.0.0"
    type-is "^2.0.0"

boolbase@^1.0.0:
  version "1.0.0"

bowser@^2.11.0:
  version "2.11.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

broadcast-channel@^7.0.0:
  version "7.0.0"
  dependencies:
    "@babel/runtime" "7.23.4"
    oblivious-set "1.4.0"
    p-queue "6.6.2"
    unload "2.4.1"

browserslist@^4.0.0, browserslist@^4.23.0, browserslist@^4.23.1:
  version "4.23.2"
  dependencies:
    caniuse-lite "^1.0.30001640"
    electron-to-chromium "^1.4.820"
    node-releases "^2.0.14"
    update-browserslist-db "^1.1.0"

browserslist@^4.24.4:
  version "4.24.5"
  dependencies:
    caniuse-lite "^1.0.30001716"
    electron-to-chromium "^1.5.149"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

buffer-crc32@^1.0.0:
  version "1.0.0"

buffer-from@^1.0.0:
  version "1.1.2"

buffer@^6.0.3:
  version "6.0.3"
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

builtin-modules@^3.3.0:
  version "3.3.0"

builtin-modules@^5.0.0:
  version "5.0.0"

bundle-name@^4.1.0:
  version "4.1.0"
  dependencies:
    run-applescript "^7.0.0"

bundle-require@^5.0.0:
  version "5.0.0"
  dependencies:
    load-tsconfig "^0.2.3"

bytes@^3.1.2, bytes@3.1.2:
  version "3.1.2"

c12@^1.11.1, c12@^1.4.2:
  version "1.11.1"
  dependencies:
    chokidar "^3.6.0"
    confbox "^0.1.7"
    defu "^6.1.4"
    dotenv "^16.4.5"
    giget "^1.2.3"
    jiti "^1.21.6"
    mlly "^1.7.1"
    ohash "^1.1.3"
    pathe "^1.1.2"
    perfect-debounce "^1.0.0"
    pkg-types "^1.1.1"
    rc9 "^2.1.2"

cac@^6.7.14, cac@^6.7.3:
  version "6.7.14"

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7:
  version "1.0.7"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.1"

call-bound@^1.0.2:
  version "1.0.4"
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"

camelcase@^6.3.0:
  version "6.3.0"

caniuse-api@^3.0.0:
  version "3.0.0"
  dependencies:
    browserslist "^4.0.0"
    caniuse-lite "^1.0.0"
    lodash.memoize "^4.1.2"
    lodash.uniq "^4.5.0"

caniuse-lite@^1.0.0, caniuse-lite@^1.0.30001599, caniuse-lite@^1.0.30001640:
  version "1.0.30001641"

caniuse-lite@^1.0.30001716:
  version "1.0.30001717"

ccount@^2.0.0:
  version "2.0.1"

chalk@^2.4.2:
  version "2.4.2"
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.1.1:
  version "4.1.2"
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^5.3.0, chalk@~5.3.0:
  version "5.3.0"

character-entities@^2.0.0:
  version "2.0.2"

cheerio-select@^2.1.0:
  version "2.1.0"
  dependencies:
    boolbase "^1.0.0"
    css-select "^5.1.0"
    css-what "^6.1.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"

cheerio@^1.0.0-rc.3:
  version "1.0.0-rc.12"
  dependencies:
    cheerio-select "^2.1.0"
    dom-serializer "^2.0.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    htmlparser2 "^8.0.1"
    parse5 "^7.0.0"
    parse5-htmlparser2-tree-adapter "^7.0.0"

chokidar@^3.5.1, chokidar@^3.6.0, "chokidar@>=3.0.0 <4.0.0":
  version "3.6.0"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chownr@^2.0.0:
  version "2.0.0"

ci-info@^4.0.0:
  version "4.0.0"

ci-info@^4.2.0:
  version "4.2.0"

citty@^0.1.5, citty@^0.1.6:
  version "0.1.6"
  dependencies:
    consola "^3.2.3"

clean-regexp@^1.0.0:
  version "1.0.0"
  dependencies:
    escape-string-regexp "^1.0.5"

clear@^0.1.0:
  version "0.1.0"

cli-cursor@^4.0.0:
  version "4.0.0"
  dependencies:
    restore-cursor "^4.0.0"

cli-truncate@^4.0.0:
  version "4.0.0"
  dependencies:
    slice-ansi "^5.0.0"
    string-width "^7.0.0"

clipboardy@^4.0.0:
  version "4.0.0"
  dependencies:
    execa "^8.0.1"
    is-wsl "^3.1.0"
    is64bit "^2.0.0"

cliui@^8.0.1:
  version "8.0.1"
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

cluster-key-slot@^1.1.0:
  version "1.1.2"

color-convert@^1.9.0, color-convert@^1.9.3:
  version "1.9.3"
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  dependencies:
    color-name "~1.1.4"

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"

color-name@1.1.3:
  version "1.1.3"

color-string@^1.6.0:
  version "1.9.1"
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

color-support@^1.1.2:
  version "1.1.3"

color@^3.1.3:
  version "3.2.1"
  dependencies:
    color-convert "^1.9.3"
    color-string "^1.6.0"

colord@^2.9.3:
  version "2.9.3"

colorette@^2.0.20:
  version "2.0.20"

colors@^1.2.1:
  version "1.4.0"

colorspace@1.1.x:
  version "1.1.4"
  dependencies:
    color "^3.1.3"
    text-hex "1.0.x"

combined-stream@^1.0.8:
  version "1.0.8"
  dependencies:
    delayed-stream "~1.0.0"

commander@^2.20.0:
  version "2.20.3"

commander@^4.0.0:
  version "4.1.1"

commander@^7.2.0:
  version "7.2.0"

commander@^8.0.0:
  version "8.3.0"

commander@~12.1.0:
  version "12.1.0"

comment-parser@^1.4.0, comment-parser@^1.4.1, comment-parser@1.4.1:
  version "1.4.1"

commondir@^1.0.1:
  version "1.0.1"

compare-func@^2.0.0:
  version "2.0.0"
  dependencies:
    array-ify "^1.0.0"
    dot-prop "^5.1.0"

compare-versions@^3.6.0:
  version "3.6.0"

compatx@^0.1.8:
  version "0.1.8"

component-emitter@^1.3.0:
  version "1.3.1"

compress-commons@^6.0.2:
  version "6.0.2"
  dependencies:
    crc-32 "^1.2.0"
    crc32-stream "^6.0.0"
    is-stream "^2.0.1"
    normalize-path "^3.0.0"
    readable-stream "^4.0.0"

concat-map@0.0.1:
  version "0.0.1"

confbox@^0.1.7:
  version "0.1.7"

confbox@^0.1.8:
  version "0.1.8"

confbox@^0.2.1:
  version "0.2.2"

connect@^3.7.0:
  version "3.7.0"
  dependencies:
    debug "2.6.9"
    finalhandler "1.1.2"
    parseurl "~1.3.3"
    utils-merge "1.0.1"

consola@^3.2.3:
  version "3.2.3"

console-control-strings@^1.0.0, console-control-strings@^1.1.0:
  version "1.1.0"

content-disposition@^1.0.0:
  version "1.0.0"
  dependencies:
    safe-buffer "5.2.1"

content-type@^1.0.5:
  version "1.0.5"

conventional-changelog-angular@^7.0.0:
  version "7.0.0"
  dependencies:
    compare-func "^2.0.0"

conventional-changelog-conventionalcommits@^7.0.2:
  version "7.0.2"
  dependencies:
    compare-func "^2.0.0"

conventional-commits-parser@^5.0.0:
  version "5.0.0"
  dependencies:
    is-text-path "^2.0.0"
    JSONStream "^1.3.5"
    meow "^12.0.1"
    split2 "^4.0.0"

convert-source-map@^2.0.0:
  version "2.0.0"

cookie-es@^1.1.0:
  version "1.1.0"

cookie-signature@^1.2.1:
  version "1.2.2"

cookie@^0.7.1:
  version "0.7.2"

cookiejar@^2.1.2:
  version "2.1.4"

cookiejs@^2.1.3:
  version "2.1.3"

copy-anything@^3.0.2:
  version "3.0.5"
  dependencies:
    is-what "^4.1.8"

core-js-compat@^3.37.0:
  version "3.37.1"
  dependencies:
    browserslist "^4.23.0"

core-js-compat@^3.41.0:
  version "3.42.0"
  dependencies:
    browserslist "^4.24.4"

core-util-is@~1.0.0:
  version "1.0.3"

cors@^2.8.5:
  version "2.8.5"
  dependencies:
    object-assign "^4"
    vary "^1"

cosmiconfig-typescript-loader@^5.0.0:
  version "5.0.0"
  dependencies:
    jiti "^1.19.1"

cosmiconfig@^9.0.0:
  version "9.0.0"
  dependencies:
    env-paths "^2.2.1"
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"

crc-32@^1.2.0:
  version "1.2.2"

crc32-stream@^6.0.0:
  version "6.0.0"
  dependencies:
    crc-32 "^1.2.0"
    readable-stream "^4.0.0"

create-require@^1.1.1:
  version "1.1.1"

croner@^8.0.2:
  version "8.1.0"

cronstrue@^2.50.0:
  version "2.50.0"

cross-spawn@^7.0.0, cross-spawn@^7.0.3:
  version "7.0.3"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

cross-spawn@^7.0.6:
  version "7.0.6"
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crossws@^0.2.0, crossws@^0.2.4:
  version "0.2.4"

css-declaration-sorter@^7.2.0:
  version "7.2.0"

css-select@^5.1.0:
  version "5.1.0"
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-tree@^2.3.1:
  version "2.3.1"
  dependencies:
    mdn-data "2.0.30"
    source-map-js "^1.0.1"

css-tree@~2.2.0:
  version "2.2.1"
  dependencies:
    mdn-data "2.0.28"
    source-map-js "^1.0.1"

css-what@^6.1.0:
  version "6.1.0"

cssesc@^3.0.0:
  version "3.0.0"

cssnano-preset-default@^7.0.4:
  version "7.0.4"
  dependencies:
    browserslist "^4.23.1"
    css-declaration-sorter "^7.2.0"
    cssnano-utils "^5.0.0"
    postcss-calc "^10.0.0"
    postcss-colormin "^7.0.1"
    postcss-convert-values "^7.0.2"
    postcss-discard-comments "^7.0.1"
    postcss-discard-duplicates "^7.0.0"
    postcss-discard-empty "^7.0.0"
    postcss-discard-overridden "^7.0.0"
    postcss-merge-longhand "^7.0.2"
    postcss-merge-rules "^7.0.2"
    postcss-minify-font-values "^7.0.0"
    postcss-minify-gradients "^7.0.0"
    postcss-minify-params "^7.0.1"
    postcss-minify-selectors "^7.0.2"
    postcss-normalize-charset "^7.0.0"
    postcss-normalize-display-values "^7.0.0"
    postcss-normalize-positions "^7.0.0"
    postcss-normalize-repeat-style "^7.0.0"
    postcss-normalize-string "^7.0.0"
    postcss-normalize-timing-functions "^7.0.0"
    postcss-normalize-unicode "^7.0.1"
    postcss-normalize-url "^7.0.0"
    postcss-normalize-whitespace "^7.0.0"
    postcss-ordered-values "^7.0.1"
    postcss-reduce-initial "^7.0.1"
    postcss-reduce-transforms "^7.0.0"
    postcss-svgo "^7.0.1"
    postcss-unique-selectors "^7.0.1"

cssnano-utils@^5.0.0:
  version "5.0.0"

cssnano@^7.0.3:
  version "7.0.4"
  dependencies:
    cssnano-preset-default "^7.0.4"
    lilconfig "^3.1.2"

csso@^5.0.5:
  version "5.0.5"
  dependencies:
    css-tree "~2.2.0"

csstype@^3.1.0, csstype@^3.1.3:
  version "3.1.3"

dargs@^8.0.0:
  version "8.1.0"

db0@^0.1.4:
  version "0.1.4"

debug@^3.2.7:
  version "3.2.7"
  dependencies:
    ms "^2.1.1"

debug@^4.0.0:
  version "4.4.0"
  dependencies:
    ms "^2.1.3"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4, debug@^4.3.5, debug@~4.3.4, debug@4:
  version "4.3.5"
  dependencies:
    ms "2.1.2"

debug@^4.3.6:
  version "4.4.0"
  dependencies:
    ms "^2.1.3"

debug@^4.4.0:
  version "4.4.0"
  dependencies:
    ms "^2.1.3"

debug@2.6.9:
  version "2.6.9"
  dependencies:
    ms "2.0.0"

declass@^0.0.1:
  version "0.0.1"
  dependencies:
    cheerio "^1.0.0-rc.3"

decode-named-character-reference@^1.0.0:
  version "1.1.0"
  dependencies:
    character-entities "^2.0.0"

deep-is@^0.1.3:
  version "0.1.4"

deepmerge@^4.2.2:
  version "4.3.1"

default-browser-id@^5.0.0:
  version "5.0.0"

default-browser@^5.2.1:
  version "5.2.1"
  dependencies:
    bundle-name "^4.1.0"
    default-browser-id "^5.0.0"

define-data-property@^1.1.4:
  version "1.1.4"
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^2.0.0:
  version "2.0.0"

define-lazy-prop@^3.0.0:
  version "3.0.0"

defu@^6.1.2, defu@^6.1.3, defu@^6.1.4:
  version "6.1.4"

delayed-stream@~1.0.0:
  version "1.0.0"

delegates@^1.0.0:
  version "1.0.0"

denque@^2.1.0:
  version "2.1.0"

depd@^2.0.0, depd@2.0.0:
  version "2.0.0"

dequal@^2.0.0:
  version "2.0.3"

destr@^2.0.3:
  version "2.0.3"

destroy@1.2.0:
  version "1.2.0"

detect-libc@^1.0.3:
  version "1.0.3"

detect-libc@^2.0.0:
  version "2.0.3"

devalue@^5.0.0:
  version "5.0.0"

devlop@^1.0.0, devlop@^1.1.0:
  version "1.1.0"
  dependencies:
    dequal "^2.0.0"

devtools-detector@^2.0.22:
  version "2.0.23"
  dependencies:
    compare-versions "^3.6.0"

diff@^5.2.0:
  version "5.2.0"

dir-glob@^3.0.1:
  version "3.0.1"
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  dependencies:
    esutils "^2.0.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom-walk@^0.1.0:
  version "0.1.2"

domelementtype@^2.3.0:
  version "2.3.0"

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  dependencies:
    domelementtype "^2.3.0"

dompurify@^3.1.6:
  version "3.1.6"

domutils@^3.0.1:
  version "3.1.0"
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dot-prop@^5.1.0:
  version "5.3.0"
  dependencies:
    is-obj "^2.0.0"

dot-prop@^8.0.2:
  version "8.0.2"
  dependencies:
    type-fest "^3.8.0"

dotenv@^16.4.5:
  version "16.4.5"

dunder-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

duplexer@^0.1.2:
  version "0.1.2"

eastasianwidth@^0.2.0:
  version "0.2.0"

ee-first@1.1.1:
  version "1.1.1"

electron-to-chromium@^1.4.820:
  version "1.4.827"

electron-to-chromium@^1.5.149:
  version "1.5.151"

emoji-regex@^10.3.0:
  version "10.3.0"

emoji-regex@^8.0.0:
  version "8.0.0"

emoji-regex@^9.2.2:
  version "9.2.2"

emojis-list@^3.0.0:
  version "3.0.0"

enabled@2.0.x:
  version "2.0.0"

encodeurl@^2.0.0:
  version "2.0.0"

encodeurl@~1.0.2:
  version "1.0.2"

enhanced-resolve@^4.1.1:
  version "4.5.0"
  dependencies:
    graceful-fs "^4.1.2"
    memory-fs "^0.5.0"
    tapable "^1.0.0"

enhanced-resolve@^5.14.1:
  version "5.17.0"
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

enhanced-resolve@^5.17.1:
  version "5.18.1"
  dependencies:
    graceful-fs "^4.2.4"
    tapable "^2.2.0"

entities@^4.2.0, entities@^4.4.0, entities@^4.5.0:
  version "4.5.0"

env-paths@^2.2.1:
  version "2.2.1"

errno@^0.1.3:
  version "0.1.8"
  dependencies:
    prr "~1.0.1"

error-ex@^1.3.1:
  version "1.3.2"
  dependencies:
    is-arrayish "^0.2.1"

error-stack-parser-es@^0.1.1, error-stack-parser-es@^0.1.4:
  version "0.1.4"

es-define-property@^1.0.0:
  version "1.0.0"
  dependencies:
    get-intrinsic "^1.2.4"

es-define-property@^1.0.1:
  version "1.0.1"

es-errors@^1.3.0:
  version "1.3.0"

es-module-lexer@^1.5.3:
  version "1.5.4"

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  dependencies:
    es-errors "^1.3.0"

esbuild@^0.20.2:
  version "0.20.2"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.20.2"
    "@esbuild/android-arm" "0.20.2"
    "@esbuild/android-arm64" "0.20.2"
    "@esbuild/android-x64" "0.20.2"
    "@esbuild/darwin-arm64" "0.20.2"
    "@esbuild/darwin-x64" "0.20.2"
    "@esbuild/freebsd-arm64" "0.20.2"
    "@esbuild/freebsd-x64" "0.20.2"
    "@esbuild/linux-arm" "0.20.2"
    "@esbuild/linux-arm64" "0.20.2"
    "@esbuild/linux-ia32" "0.20.2"
    "@esbuild/linux-loong64" "0.20.2"
    "@esbuild/linux-mips64el" "0.20.2"
    "@esbuild/linux-ppc64" "0.20.2"
    "@esbuild/linux-riscv64" "0.20.2"
    "@esbuild/linux-s390x" "0.20.2"
    "@esbuild/linux-x64" "0.20.2"
    "@esbuild/netbsd-x64" "0.20.2"
    "@esbuild/openbsd-x64" "0.20.2"
    "@esbuild/sunos-x64" "0.20.2"
    "@esbuild/win32-arm64" "0.20.2"
    "@esbuild/win32-ia32" "0.20.2"
    "@esbuild/win32-x64" "0.20.2"

esbuild@^0.21.3, esbuild@^0.21.5:
  version "0.21.5"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

esbuild@^0.23.0:
  version "0.23.0"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.23.0"
    "@esbuild/android-arm" "0.23.0"
    "@esbuild/android-arm64" "0.23.0"
    "@esbuild/android-x64" "0.23.0"
    "@esbuild/darwin-arm64" "0.23.0"
    "@esbuild/darwin-x64" "0.23.0"
    "@esbuild/freebsd-arm64" "0.23.0"
    "@esbuild/freebsd-x64" "0.23.0"
    "@esbuild/linux-arm" "0.23.0"
    "@esbuild/linux-arm64" "0.23.0"
    "@esbuild/linux-ia32" "0.23.0"
    "@esbuild/linux-loong64" "0.23.0"
    "@esbuild/linux-mips64el" "0.23.0"
    "@esbuild/linux-ppc64" "0.23.0"
    "@esbuild/linux-riscv64" "0.23.0"
    "@esbuild/linux-s390x" "0.23.0"
    "@esbuild/linux-x64" "0.23.0"
    "@esbuild/netbsd-x64" "0.23.0"
    "@esbuild/openbsd-arm64" "0.23.0"
    "@esbuild/openbsd-x64" "0.23.0"
    "@esbuild/sunos-x64" "0.23.0"
    "@esbuild/win32-arm64" "0.23.0"
    "@esbuild/win32-ia32" "0.23.0"
    "@esbuild/win32-x64" "0.23.0"

escalade@^3.1.1, escalade@^3.1.2:
  version "3.1.2"

escalade@^3.2.0:
  version "3.2.0"

escape-html@^1.0.3, escape-html@~1.0.3:
  version "1.0.3"

escape-string-regexp@^1.0.5:
  version "1.0.5"

escape-string-regexp@^4.0.0:
  version "4.0.0"

escape-string-regexp@^5.0.0:
  version "5.0.0"

escape-string-regexp@4.0.0:
  version "4.0.0"

escodegen@^2.1.0:
  version "2.1.0"
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
  optionalDependencies:
    source-map "~0.6.1"

eslint-compat-utils@^0.5.1:
  version "0.5.1"
  dependencies:
    semver "^7.5.4"

eslint-compat-utils@^0.6.0, eslint-compat-utils@^0.6.4:
  version "0.6.5"
  dependencies:
    semver "^7.5.4"

eslint-config-flat-gitignore@^0.1.5:
  version "0.1.8"
  dependencies:
    find-up-simple "^1.0.0"
    parse-gitignore "^2.0.0"

eslint-config-flat-gitignore@^2.1.0:
  version "2.1.0"
  dependencies:
    "@eslint/compat" "^1.2.5"

eslint-flat-config-utils@^0.2.4, eslint-flat-config-utils@^0.2.5:
  version "0.2.5"
  dependencies:
    "@types/eslint" "^8.56.10"
    pathe "^1.1.2"

eslint-flat-config-utils@^2.0.1:
  version "2.0.1"
  dependencies:
    pathe "^2.0.2"

eslint-formatting-reporter@^0.0.0:
  version "0.0.0"
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-import-resolver-node@^0.3.9:
  version "0.3.9"
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-json-compat-utils@^0.2.1:
  version "0.2.1"
  dependencies:
    esquery "^1.6.0"

eslint-merge-processors@^2.0.0:
  version "2.0.0"

eslint-parser-plain@^0.1.1:
  version "0.1.1"

eslint-plugin-antfu@^3.1.1:
  version "3.1.1"

eslint-plugin-command@^3.2.0:
  version "3.2.0"
  dependencies:
    "@es-joy/jsdoccomment" "^0.50.0"

eslint-plugin-es-x@^7.8.0:
  version "7.8.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.1.2"
    "@eslint-community/regexpp" "^4.11.0"
    eslint-compat-utils "^0.5.1"

eslint-plugin-format@^1.0.1:
  version "1.0.1"
  dependencies:
    "@dprint/formatter" "^0.3.0"
    "@dprint/markdown" "^0.17.8"
    "@dprint/toml" "^0.6.4"
    eslint-formatting-reporter "^0.0.0"
    eslint-parser-plain "^0.1.1"
    prettier "^3.4.2"
    synckit "^0.9.2"

eslint-plugin-import-x@^0.5.0:
  version "0.5.3"
  dependencies:
    "@typescript-eslint/utils" "^7.4.0"
    debug "^4.3.4"
    doctrine "^3.0.0"
    eslint-import-resolver-node "^0.3.9"
    get-tsconfig "^4.7.3"
    is-glob "^4.0.3"
    minimatch "^9.0.3"
    semver "^7.6.0"
    stable-hash "^0.0.4"
    tslib "^2.6.2"

eslint-plugin-import-x@^4.11.0:
  version "4.11.0"
  dependencies:
    "@typescript-eslint/utils" "^8.31.0"
    comment-parser "^1.4.1"
    debug "^4.4.0"
    eslint-import-resolver-node "^0.3.9"
    get-tsconfig "^4.10.0"
    is-glob "^4.0.3"
    minimatch "^9.0.3 || ^10.0.1"
    semver "^7.7.1"
    stable-hash "^0.0.5"
    tslib "^2.8.1"
    unrs-resolver "^1.7.0"

eslint-plugin-jsdoc@^48.2.5:
  version "48.7.0"
  dependencies:
    "@es-joy/jsdoccomment" "~0.46.0"
    are-docs-informative "^0.0.2"
    comment-parser "1.4.1"
    debug "^4.3.5"
    escape-string-regexp "^4.0.0"
    esquery "^1.6.0"
    parse-imports "^2.1.1"
    semver "^7.6.2"
    spdx-expression-parse "^4.0.0"
    synckit "^0.9.0"

eslint-plugin-jsdoc@^50.6.11:
  version "50.6.11"
  dependencies:
    "@es-joy/jsdoccomment" "~0.49.0"
    are-docs-informative "^0.0.2"
    comment-parser "1.4.1"
    debug "^4.3.6"
    escape-string-regexp "^4.0.0"
    espree "^10.1.0"
    esquery "^1.6.0"
    parse-imports-exports "^0.2.4"
    semver "^7.6.3"
    spdx-expression-parse "^4.0.0"

eslint-plugin-jsonc@^2.20.0:
  version "2.20.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.5.1"
    eslint-compat-utils "^0.6.4"
    eslint-json-compat-utils "^0.2.1"
    espree "^9.6.1 || ^10.3.0"
    graphemer "^1.4.0"
    jsonc-eslint-parser "^2.4.0"
    natural-compare "^1.4.0"
    synckit "^0.6.2 || ^0.7.3 || ^0.10.3"

eslint-plugin-n@^17.17.0:
  version "17.18.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.5.0"
    enhanced-resolve "^5.17.1"
    eslint-plugin-es-x "^7.8.0"
    get-tsconfig "^4.8.1"
    globals "^15.11.0"
    ignore "^5.3.2"
    minimatch "^9.0.5"
    semver "^7.6.3"

eslint-plugin-no-only-tests@^3.3.0:
  version "3.3.0"

eslint-plugin-perfectionist@^4.12.3:
  version "4.12.3"
  dependencies:
    "@typescript-eslint/types" "^8.31.0"
    "@typescript-eslint/utils" "^8.31.0"
    natural-orderby "^5.0.0"

eslint-plugin-pnpm@^0.3.1:
  version "0.3.1"
  dependencies:
    find-up-simple "^1.0.1"
    jsonc-eslint-parser "^2.4.0"
    pathe "^2.0.3"
    pnpm-workspace-yaml "0.3.1"
    tinyglobby "^0.2.12"
    yaml-eslint-parser "^1.3.0"

eslint-plugin-regexp@^2.5.0:
  version "2.6.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.9.1"
    comment-parser "^1.4.0"
    jsdoc-type-pratt-parser "^4.0.0"
    refa "^0.12.1"
    regexp-ast-analysis "^0.7.1"
    scslre "^0.3.0"

eslint-plugin-regexp@^2.7.0:
  version "2.7.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.11.0"
    comment-parser "^1.4.0"
    jsdoc-type-pratt-parser "^4.0.0"
    refa "^0.12.1"
    regexp-ast-analysis "^0.7.1"
    scslre "^0.3.0"

eslint-plugin-toml@^0.12.0:
  version "0.12.0"
  dependencies:
    debug "^4.1.1"
    eslint-compat-utils "^0.6.0"
    lodash "^4.17.19"
    toml-eslint-parser "^0.10.0"

eslint-plugin-unicorn@^53.0.0:
  version "53.0.0"
  dependencies:
    "@babel/helper-validator-identifier" "^7.24.5"
    "@eslint-community/eslint-utils" "^4.4.0"
    "@eslint/eslintrc" "^3.0.2"
    ci-info "^4.0.0"
    clean-regexp "^1.0.0"
    core-js-compat "^3.37.0"
    esquery "^1.5.0"
    indent-string "^4.0.0"
    is-builtin-module "^3.2.1"
    jsesc "^3.0.2"
    pluralize "^8.0.0"
    read-pkg-up "^7.0.1"
    regexp-tree "^0.1.27"
    regjsparser "^0.10.0"
    semver "^7.6.1"
    strip-indent "^3.0.0"

eslint-plugin-unicorn@^59.0.1:
  version "59.0.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    "@eslint-community/eslint-utils" "^4.5.1"
    "@eslint/plugin-kit" "^0.2.7"
    ci-info "^4.2.0"
    clean-regexp "^1.0.0"
    core-js-compat "^3.41.0"
    esquery "^1.6.0"
    find-up-simple "^1.0.1"
    globals "^16.0.0"
    indent-string "^5.0.0"
    is-builtin-module "^5.0.0"
    jsesc "^3.1.0"
    pluralize "^8.0.0"
    regexp-tree "^0.1.27"
    regjsparser "^0.12.0"
    semver "^7.7.1"
    strip-indent "^4.0.0"

eslint-plugin-unused-imports@^4.1.4:
  version "4.1.4"

eslint-plugin-vue@^10.1.0:
  version "10.1.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    natural-compare "^1.4.0"
    nth-check "^2.1.1"
    postcss-selector-parser "^6.0.15"
    semver "^7.6.3"
    xml-name-validator "^4.0.0"

eslint-plugin-vue@^9.26.0:
  version "9.27.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.4.0"
    globals "^13.24.0"
    natural-compare "^1.4.0"
    nth-check "^2.1.1"
    postcss-selector-parser "^6.0.15"
    semver "^7.6.0"
    vue-eslint-parser "^9.4.3"
    xml-name-validator "^4.0.0"

eslint-plugin-yml@^1.18.0:
  version "1.18.0"
  dependencies:
    debug "^4.3.2"
    escape-string-regexp "4.0.0"
    eslint-compat-utils "^0.6.0"
    natural-compare "^1.4.0"
    yaml-eslint-parser "^1.2.1"

eslint-processor-vue-blocks@^2.0.0:
  version "2.0.0"

eslint-scope@^7.1.1:
  version "7.2.2"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-scope@^8.2.0, eslint-scope@^8.3.0:
  version "8.3.0"
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-typegen@^0.2.4:
  version "0.2.4"
  dependencies:
    "@types/eslint" "^8.56.10"
    json-schema-to-typescript-lite "^14.0.1"
    ohash "^1.1.3"

eslint-visitor-keys@^3.0.0, eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"

eslint-visitor-keys@^4.0.0:
  version "4.0.0"

eslint-visitor-keys@^4.2.0:
  version "4.2.0"

eslint@^9.26.0:
  version "9.26.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.20.0"
    "@eslint/config-helpers" "^0.2.1"
    "@eslint/core" "^0.13.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.26.0"
    "@eslint/plugin-kit" "^0.2.8"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@modelcontextprotocol/sdk" "^1.8.0"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.6"
    debug "^4.3.2"
    escape-string-regexp "^4.0.0"
    eslint-scope "^8.3.0"
    eslint-visitor-keys "^4.2.0"
    espree "^10.3.0"
    esquery "^1.5.0"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^8.0.0"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    zod "^3.24.2"

espree@^10.0.1:
  version "10.1.0"
  dependencies:
    acorn "^8.12.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.0.0"

espree@^10.1.0:
  version "10.1.0"
  dependencies:
    acorn "^8.12.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.0.0"

espree@^10.3.0, "espree@^9.6.1 || ^10.3.0":
  version "10.3.0"
  dependencies:
    acorn "^8.14.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^4.2.0"

espree@^9.0.0:
  version "9.6.1"
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

espree@^9.3.1:
  version "9.6.1"
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.1:
  version "4.0.1"

esquery@^1.4.0, esquery@^1.5.0, esquery@^1.6.0:
  version "1.6.0"
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"

estree-walker@^2.0.1, estree-walker@^2.0.2, estree-walker@2.0.2:
  version "2.0.2"

estree-walker@^3.0.3:
  version "3.0.3"
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"

etag@^1.8.1, etag@~1.8.1:
  version "1.8.1"

event-target-shim@^5.0.0:
  version "5.0.1"

eventemitter3@^4.0.4:
  version "4.0.7"

eventemitter3@^5.0.1:
  version "5.0.1"

events@^3.3.0:
  version "3.3.0"

eventsource-parser@^3.0.1:
  version "3.0.1"

eventsource@^3.0.2:
  version "3.0.6"
  dependencies:
    eventsource-parser "^3.0.1"

execa@^7.2.0:
  version "7.2.0"
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

execa@^8.0.1, execa@~8.0.1:
  version "8.0.1"
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^8.0.1"
    human-signals "^5.0.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^4.1.0"
    strip-final-newline "^3.0.0"

express-rate-limit@^7.5.0:
  version "7.5.0"

express@^5.0.1:
  version "5.1.0"
  dependencies:
    accepts "^2.0.0"
    body-parser "^2.2.0"
    content-disposition "^1.0.0"
    content-type "^1.0.5"
    cookie "^0.7.1"
    cookie-signature "^1.2.1"
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    finalhandler "^2.1.0"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    merge-descriptors "^2.0.0"
    mime-types "^3.0.0"
    on-finished "^2.4.1"
    once "^1.4.0"
    parseurl "^1.3.3"
    proxy-addr "^2.0.7"
    qs "^6.14.0"
    range-parser "^1.2.1"
    router "^2.2.0"
    send "^1.1.0"
    serve-static "^2.2.0"
    statuses "^2.0.1"
    type-is "^2.0.1"
    vary "^1.1.2"

exsolve@^1.0.1:
  version "1.0.5"

externality@^1.0.2:
  version "1.0.2"
  dependencies:
    enhanced-resolve "^5.14.1"
    mlly "^1.3.0"
    pathe "^1.1.1"
    ufo "^1.1.2"

fast-copy@^3.0.2:
  version "3.0.2"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"

fast-diff@^1.1.2:
  version "1.3.0"

fast-equals@^5.0.1:
  version "5.0.1"

fast-fifo@^1.2.0, fast-fifo@^1.3.2:
  version "1.3.2"

fast-glob@^3.2.12, fast-glob@^3.2.6, fast-glob@^3.2.7, fast-glob@^3.2.9, fast-glob@^3.3.0, fast-glob@^3.3.2:
  version "3.3.2"
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"

fast-levenshtein@^2.0.6:
  version "2.0.6"

fast-npm-meta@^0.1.1:
  version "0.1.1"

fast-safe-stringify@^2.0.4, fast-safe-stringify@^2.0.7:
  version "2.1.1"

fast-uri@^3.0.1:
  version "3.0.1"

fastq@^1.6.0:
  version "1.17.1"
  dependencies:
    reusify "^1.0.4"

fault@^2.0.0:
  version "2.0.1"
  dependencies:
    format "^0.2.0"

fdir@^6.4.4:
  version "6.4.4"

fecha@^2.3.3:
  version "2.3.3"

fecha@^4.2.0:
  version "4.2.3"

file-entry-cache@^8.0.0:
  version "8.0.0"
  dependencies:
    flat-cache "^4.0.0"

file-uri-to-path@1.0.0:
  version "1.0.0"

fill-range@^7.1.1:
  version "7.1.1"
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@^2.1.0:
  version "2.1.0"
  dependencies:
    debug "^4.4.0"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    on-finished "^2.4.1"
    parseurl "^1.3.3"
    statuses "^2.0.1"

finalhandler@1.1.2:
  version "1.1.2"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.3"
    statuses "~1.5.0"
    unpipe "~1.0.0"

find-up-simple@^1.0.0:
  version "1.0.0"

find-up-simple@^1.0.1:
  version "1.0.1"

find-up@^4.1.0:
  version "4.1.0"
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

find-up@^7.0.0:
  version "7.0.0"
  dependencies:
    locate-path "^7.2.0"
    path-exists "^5.0.0"
    unicorn-magic "^0.1.0"

flat-cache@^4.0.0:
  version "4.0.1"
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.4"

flatted@^3.2.9, flatted@^3.3.1:
  version "3.3.1"

fn.name@1.x.x:
  version "1.1.0"

foreground-child@^3.1.0:
  version "3.2.1"
  dependencies:
    cross-spawn "^7.0.0"
    signal-exit "^4.0.1"

form-data@^3.0.0:
  version "3.0.1"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

format@^0.2.0:
  version "0.2.2"

formidable@^1.2.1:
  version "1.2.6"

forwarded@0.2.0:
  version "0.2.0"

fraction.js@^4.3.7:
  version "4.3.7"

fresh@^2.0.0:
  version "2.0.0"

fresh@0.5.2:
  version "0.5.2"

fs-extra@^10.0.0:
  version "10.1.0"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^11.1.0, fs-extra@^11.2.0:
  version "11.2.0"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-minipass@^2.0.0:
  version "2.1.0"
  dependencies:
    minipass "^3.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"

function-bind@^1.1.2:
  version "1.1.2"

gauge@^3.0.0:
  version "3.0.2"
  dependencies:
    aproba "^1.0.3 || ^2.0.0"
    color-support "^1.1.2"
    console-control-strings "^1.0.0"
    has-unicode "^2.0.1"
    object-assign "^4.1.1"
    signal-exit "^3.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"
    wide-align "^1.1.2"

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"

get-caller-file@^2.0.5:
  version "2.0.5"

get-east-asian-width@^1.0.0:
  version "1.2.0"

get-intrinsic@^1.1.3, get-intrinsic@^1.2.4:
  version "1.2.4"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    has-proto "^1.0.1"
    has-symbols "^1.0.3"
    hasown "^2.0.0"

get-intrinsic@^1.2.5:
  version "1.3.0"
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-intrinsic@^1.3.0:
  version "1.3.0"
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-port-please@^3.1.1, get-port-please@^3.1.2:
  version "3.1.2"

get-port@^6.1.2:
  version "6.1.2"

get-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.1:
  version "6.0.1"

get-stream@^8.0.1:
  version "8.0.1"

get-tsconfig@^4.10.0, get-tsconfig@^4.8.1:
  version "4.10.0"
  dependencies:
    resolve-pkg-maps "^1.0.0"

get-tsconfig@^4.7.3:
  version "4.7.5"
  dependencies:
    resolve-pkg-maps "^1.0.0"

giget@^1.2.3:
  version "1.2.3"
  dependencies:
    citty "^0.1.6"
    consola "^3.2.3"
    defu "^6.1.4"
    node-fetch-native "^1.6.3"
    nypm "^0.3.8"
    ohash "^1.1.3"
    pathe "^1.1.2"
    tar "^6.2.0"

git-config-path@^2.0.0:
  version "2.0.0"

git-raw-commits@^4.0.0:
  version "4.0.0"
  dependencies:
    dargs "^8.0.0"
    meow "^12.0.1"
    split2 "^4.0.0"

git-up@^7.0.0:
  version "7.0.0"
  dependencies:
    is-ssh "^1.4.0"
    parse-url "^8.1.0"

git-url-parse@^14.0.0:
  version "14.0.0"
  dependencies:
    git-up "^7.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  dependencies:
    is-glob "^4.0.3"

glob@^10.0.0, glob@^10.3.10:
  version "10.4.5"
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.1.3:
  version "7.2.3"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

glob@^8.0.3:
  version "8.1.0"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^5.0.1"
    once "^1.3.0"

global-directory@^4.0.1:
  version "4.0.1"
  dependencies:
    ini "4.1.1"

global@^4.3.1, global@^4.4.0, global@~4.4.0, global@4.4.0:
  version "4.4.0"
  dependencies:
    min-document "^2.19.0"
    process "^0.11.10"

globals@^11.1.0:
  version "11.12.0"

globals@^13.24.0:
  version "13.24.0"
  dependencies:
    type-fest "^0.20.2"

globals@^14.0.0:
  version "14.0.0"

globals@^15.11.0:
  version "15.15.0"

globals@^15.2.0:
  version "15.8.0"

globals@^16.0.0:
  version "16.1.0"

globby@^11.1.0:
  version "11.1.0"
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globby@^13.2.2:
  version "13.2.2"
  dependencies:
    dir-glob "^3.0.1"
    fast-glob "^3.3.0"
    ignore "^5.2.4"
    merge2 "^1.4.1"
    slash "^4.0.0"

globby@^14.0.1, globby@^14.0.2:
  version "14.0.2"
  dependencies:
    "@sindresorhus/merge-streams" "^2.1.0"
    fast-glob "^3.3.2"
    ignore "^5.2.4"
    path-type "^5.0.0"
    slash "^5.1.0"
    unicorn-magic "^0.1.0"

gopd@^1.0.1:
  version "1.0.1"
  dependencies:
    get-intrinsic "^1.1.3"

gopd@^1.2.0:
  version "1.2.0"

graceful-fs@^4.1.2, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.9:
  version "4.2.11"

graphemer@^1.4.0:
  version "1.4.0"

graylog2@^0.2.1:
  version "0.2.1"

gzip-size@^7.0.0:
  version "7.0.0"
  dependencies:
    duplexer "^0.1.2"

h3@^1.10.2, h3@^1.11.1, h3@^1.12.0:
  version "1.12.0"
  dependencies:
    cookie-es "^1.1.0"
    crossws "^0.2.4"
    defu "^6.1.4"
    destr "^2.0.3"
    iron-webcrypto "^1.1.1"
    ohash "^1.1.3"
    radix3 "^1.1.2"
    ufo "^1.5.3"
    uncrypto "^0.1.3"
    unenv "^1.9.0"

has-flag@^3.0.0:
  version "3.0.0"

has-flag@^4.0.0:
  version "4.0.0"

has-property-descriptors@^1.0.2:
  version "1.0.2"
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.0.1:
  version "1.0.3"

has-symbols@^1.0.3:
  version "1.0.3"

has-symbols@^1.1.0:
  version "1.1.0"

has-unicode@^2.0.1:
  version "2.0.1"

hash-sum@^2.0.0:
  version "2.0.0"

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  dependencies:
    function-bind "^1.1.2"

hookable@^5.5.3:
  version "5.5.3"

hosted-git-info@^2.1.4:
  version "2.8.9"

html-tags@^3.3.1:
  version "3.3.1"

htmlparser2@^8.0.0, htmlparser2@^8.0.1:
  version "8.0.2"
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-errors@^2.0.0, http-errors@2.0.0:
  version "2.0.0"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

http-shutdown@^1.2.2:
  version "1.2.2"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  dependencies:
    agent-base "6"
    debug "4"

httpxy@^0.1.5:
  version "0.1.5"

human-signals@^4.3.0:
  version "4.3.1"

human-signals@^5.0.0:
  version "5.0.0"

husky@9.0.11:
  version "9.0.11"

iconv-lite@^0.6.3, iconv-lite@0.6.3:
  version "0.6.3"
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

ieee754@^1.2.1:
  version "1.2.1"

ignore@^5.2.0, ignore@^5.2.4, ignore@^5.3.1:
  version "5.3.1"

ignore@^5.3.2:
  version "5.3.2"

image-meta@^0.2.0:
  version "0.2.1"

immediate@~3.0.5:
  version "3.0.6"

immutable@^4.0.0:
  version "4.3.6"

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.0"
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-meta-resolve@^4.0.0:
  version "4.1.0"

imurmurhash@^0.1.4:
  version "0.1.4"

indent-string@^4.0.0:
  version "4.0.0"

indent-string@^5.0.0:
  version "5.0.0"

inflight@^1.0.4:
  version "1.0.6"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@^2.0.3, inherits@~2.0.3, inherits@2, inherits@2.0.4:
  version "2.0.4"

ini@^1.3.5:
  version "1.3.8"

ini@4.1.1:
  version "4.1.1"

ioredis@^5.4.1:
  version "5.4.1"
  dependencies:
    "@ioredis/commands" "^1.1.1"
    cluster-key-slot "^1.1.0"
    debug "^4.3.4"
    denque "^2.1.0"
    lodash.defaults "^4.2.0"
    lodash.isarguments "^3.1.0"
    redis-errors "^1.2.0"
    redis-parser "^3.0.0"
    standard-as-callback "^2.1.0"

ipaddr.js@1.9.1:
  version "1.9.1"

iron-webcrypto@^1.1.1:
  version "1.2.1"

is-arrayish@^0.2.1:
  version "0.2.1"

is-arrayish@^0.3.1:
  version "0.3.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  dependencies:
    binary-extensions "^2.0.0"

is-builtin-module@^3.2.1:
  version "3.2.1"
  dependencies:
    builtin-modules "^3.3.0"

is-builtin-module@^5.0.0:
  version "5.0.0"
  dependencies:
    builtin-modules "^5.0.0"

is-core-module@^2.13.0:
  version "2.14.0"
  dependencies:
    hasown "^2.0.2"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"

is-docker@^3.0.0:
  version "3.0.0"

is-extglob@^2.1.1:
  version "2.1.1"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"

is-fullwidth-code-point@^4.0.0:
  version "4.0.0"

is-fullwidth-code-point@^5.0.0:
  version "5.0.0"
  dependencies:
    get-east-asian-width "^1.0.0"

is-function@^1.0.1:
  version "1.0.2"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-https@^4.0.0:
  version "4.0.0"

is-inside-container@^1.0.0:
  version "1.0.0"
  dependencies:
    is-docker "^3.0.0"

is-installed-globally@^1.0.0:
  version "1.0.0"
  dependencies:
    global-directory "^4.0.1"
    is-path-inside "^4.0.0"

is-module@^1.0.0:
  version "1.0.0"

is-number@^7.0.0:
  version "7.0.0"

is-obj@^2.0.0:
  version "2.0.0"

is-path-inside@^4.0.0:
  version "4.0.0"

is-promise@^4.0.0:
  version "4.0.0"

is-reference@1.2.1:
  version "1.2.1"
  dependencies:
    "@types/estree" "*"

is-ssh@^1.4.0:
  version "1.4.0"
  dependencies:
    protocols "^2.0.1"

is-stream@^2.0.0, is-stream@^2.0.1:
  version "2.0.1"

is-stream@^3.0.0:
  version "3.0.0"

is-text-path@^2.0.0:
  version "2.0.0"
  dependencies:
    text-extensions "^2.0.0"

is-what@^4.1.8:
  version "4.1.16"

is-wsl@^2.2.0:
  version "2.2.0"
  dependencies:
    is-docker "^2.0.0"

is-wsl@^3.1.0:
  version "3.1.0"
  dependencies:
    is-inside-container "^1.0.0"

is64bit@^2.0.0:
  version "2.0.0"
  dependencies:
    system-architecture "^0.1.0"

isarray@~1.0.0:
  version "1.0.0"

isexe@^2.0.0:
  version "2.0.0"

jackspeak@^3.1.2:
  version "3.4.3"
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jiti@^1.18.2, jiti@^1.19.1, jiti@^1.20.0, jiti@^1.21.0, jiti@^1.21.6:
  version "1.21.6"

js-tokens@^4.0.0:
  version "4.0.0"

js-tokens@^9.0.0:
  version "9.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  dependencies:
    argparse "^2.0.1"

jsdoc-type-pratt-parser@^4.0.0, jsdoc-type-pratt-parser@~4.0.0:
  version "4.0.0"

jsdoc-type-pratt-parser@~4.1.0:
  version "4.1.0"

jsesc@^2.5.1:
  version "2.5.2"

jsesc@^3.0.2, jsesc@~3.0.2:
  version "3.0.2"

jsesc@^3.1.0:
  version "3.1.0"

jsesc@~0.5.0:
  version "0.5.0"

json-buffer@3.0.1:
  version "3.0.1"

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"

json-schema-to-typescript-lite@^14.0.1:
  version "14.0.1"
  dependencies:
    "@apidevtools/json-schema-ref-parser" "^11.6.0"
    "@types/json-schema" "^7.0.15"

json-schema-traverse@^0.4.1:
  version "0.4.1"

json-schema-traverse@^1.0.0:
  version "1.0.0"

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"

json5@^2.1.2, json5@^2.2.3:
  version "2.2.3"

jsonc-eslint-parser@^2.3.0, jsonc-eslint-parser@^2.4.0:
  version "2.4.0"
  dependencies:
    acorn "^8.5.0"
    eslint-visitor-keys "^3.0.0"
    espree "^9.0.0"
    semver "^7.3.5"

jsonfile@^6.0.1:
  version "6.1.0"
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonparse@^1.2.0:
  version "1.3.1"

JSONStream@^1.3.5:
  version "1.3.5"
  dependencies:
    jsonparse "^1.2.0"
    through ">=2.2.7 <3"

keyv@^4.5.4:
  version "4.5.4"
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"

klona@^2.0.6:
  version "2.0.6"

knitwork@^1.0.0, knitwork@^1.1.0:
  version "1.1.0"

kolorist@^1.8.0:
  version "1.8.0"

kuler@^2.0.0:
  version "2.0.0"

launch-editor@^2.8.0:
  version "2.8.0"
  dependencies:
    picocolors "^1.0.0"
    shell-quote "^1.8.1"

lazystream@^1.0.0:
  version "1.0.1"
  dependencies:
    readable-stream "^2.0.5"

levn@^0.4.1:
  version "0.4.1"
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

libphonenumber-js@^1.10.12:
  version "1.11.4"

lie@3.1.1:
  version "3.1.1"
  dependencies:
    immediate "~3.0.5"

lilconfig@^3.1.2, lilconfig@~3.1.1:
  version "3.1.2"

lines-and-columns@^1.1.6:
  version "1.2.4"

lint-staged@15.2.7:
  version "15.2.7"
  dependencies:
    chalk "~5.3.0"
    commander "~12.1.0"
    debug "~4.3.4"
    execa "~8.0.1"
    lilconfig "~3.1.1"
    listr2 "~8.2.1"
    micromatch "~4.0.7"
    pidtree "~0.6.0"
    string-argv "~0.3.2"
    yaml "~2.4.2"

listhen@^1.7.2:
  version "1.7.2"
  dependencies:
    "@parcel/watcher" "^2.4.1"
    "@parcel/watcher-wasm" "^2.4.1"
    citty "^0.1.6"
    clipboardy "^4.0.0"
    consola "^3.2.3"
    crossws "^0.2.0"
    defu "^6.1.4"
    get-port-please "^3.1.2"
    h3 "^1.10.2"
    http-shutdown "^1.2.2"
    jiti "^1.21.0"
    mlly "^1.6.1"
    node-forge "^1.3.1"
    pathe "^1.1.2"
    std-env "^3.7.0"
    ufo "^1.4.0"
    untun "^0.1.3"
    uqr "^0.1.2"

listr2@~8.2.1:
  version "8.2.3"
  dependencies:
    cli-truncate "^4.0.0"
    colorette "^2.0.20"
    eventemitter3 "^5.0.1"
    log-update "^6.0.0"
    rfdc "^1.4.1"
    wrap-ansi "^9.0.0"

load-tsconfig@^0.2.3:
  version "0.2.5"

loader-utils@^2.0.0:
  version "2.0.4"
  dependencies:
    big.js "^5.2.2"
    emojis-list "^3.0.0"
    json5 "^2.1.2"

local-pkg@^0.5.0:
  version "0.5.0"
  dependencies:
    mlly "^1.4.2"
    pkg-types "^1.0.3"

local-pkg@^1.1.1:
  version "1.1.1"
  dependencies:
    mlly "^1.7.4"
    pkg-types "^2.0.1"
    quansync "^0.2.8"

localforage@^1.8.1:
  version "1.10.0"
  dependencies:
    lie "3.1.1"

locate-path@^5.0.0:
  version "5.0.0"
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  dependencies:
    p-locate "^5.0.0"

locate-path@^7.2.0:
  version "7.2.0"
  dependencies:
    p-locate "^6.0.0"

lodash.camelcase@^4.3.0:
  version "4.3.0"

lodash.defaults@^4.2.0:
  version "4.2.0"

lodash.isarguments@^3.1.0:
  version "3.1.0"

lodash.isplainobject@^4.0.6:
  version "4.0.6"

lodash.kebabcase@^4.1.1:
  version "4.1.1"

lodash.memoize@^4.1.2:
  version "4.1.2"

lodash.merge@^4.6.2:
  version "4.6.2"

lodash.mergewith@^4.6.2:
  version "4.6.2"

lodash.snakecase@^4.1.1:
  version "4.1.1"

lodash.startcase@^4.4.0:
  version "4.4.0"

lodash.uniq@^4.5.0:
  version "4.5.0"

lodash.upperfirst@^4.3.1:
  version "4.3.1"

lodash@^4.17.15, lodash@^4.17.19, lodash@^4.17.21:
  version "4.17.21"

log-update@^6.0.0:
  version "6.0.0"
  dependencies:
    ansi-escapes "^6.2.0"
    cli-cursor "^4.0.0"
    slice-ansi "^7.0.0"
    strip-ansi "^7.1.0"
    wrap-ansi "^9.0.0"

logform@^2.6.0, logform@^2.6.1:
  version "2.6.1"
  dependencies:
    "@colors/colors" "1.6.0"
    "@types/triple-beam" "^1.3.2"
    fecha "^4.2.0"
    ms "^2.1.1"
    safe-stable-stringify "^2.3.1"
    triple-beam "^1.3.0"

logform@2.1.2:
  version "2.1.2"
  dependencies:
    colors "^1.2.1"
    fast-safe-stringify "^2.0.4"
    fecha "^2.3.3"
    ms "^2.1.1"
    triple-beam "^1.3.0"

longest-streak@^3.0.0:
  version "3.1.0"

lru-cache@^10.2.0:
  version "10.4.3"

lru-cache@^5.1.1:
  version "5.1.1"
  dependencies:
    yallist "^3.0.2"

m3u8-parser@^7.1.0:
  version "7.2.0"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^4.1.1"
    global "^4.4.0"

magic-string-ast@^0.6.0:
  version "0.6.2"
  dependencies:
    magic-string "^0.30.10"

magic-string@^0.30.0, magic-string@^0.30.10, magic-string@^0.30.3, magic-string@^0.30.4, magic-string@^0.30.8:
  version "0.30.10"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.15"

magicast@^0.3.4:
  version "0.3.4"
  dependencies:
    "@babel/parser" "^7.24.4"
    "@babel/types" "^7.24.0"
    source-map-js "^1.2.0"

make-dir@^3.1.0:
  version "3.1.0"
  dependencies:
    semver "^6.0.0"

markdown-table@^3.0.0:
  version "3.0.4"

math-intrinsics@^1.1.0:
  version "1.1.0"

mdast-util-find-and-replace@^3.0.0:
  version "3.0.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    escape-string-regexp "^5.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

mdast-util-from-markdown@^2.0.0, mdast-util-from-markdown@^2.0.2:
  version "2.0.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    mdast-util-to-string "^4.0.0"
    micromark "^4.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"
    unist-util-stringify-position "^4.0.0"

mdast-util-frontmatter@^2.0.1:
  version "2.0.1"
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    escape-string-regexp "^5.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    micromark-extension-frontmatter "^2.0.0"

mdast-util-gfm-autolink-literal@^2.0.0:
  version "2.0.1"
  dependencies:
    "@types/mdast" "^4.0.0"
    ccount "^2.0.0"
    devlop "^1.0.0"
    mdast-util-find-and-replace "^3.0.0"
    micromark-util-character "^2.0.0"

mdast-util-gfm-footnote@^2.0.0:
  version "2.1.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.1.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"

mdast-util-gfm-strikethrough@^2.0.0:
  version "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-table@^2.0.0:
  version "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    markdown-table "^3.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm-task-list-item@^2.0.0:
  version "2.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    devlop "^1.0.0"
    mdast-util-from-markdown "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-gfm@^3.0.0:
  version "3.1.0"
  dependencies:
    mdast-util-from-markdown "^2.0.0"
    mdast-util-gfm-autolink-literal "^2.0.0"
    mdast-util-gfm-footnote "^2.0.0"
    mdast-util-gfm-strikethrough "^2.0.0"
    mdast-util-gfm-table "^2.0.0"
    mdast-util-gfm-task-list-item "^2.0.0"
    mdast-util-to-markdown "^2.0.0"

mdast-util-phrasing@^4.0.0:
  version "4.1.0"
  dependencies:
    "@types/mdast" "^4.0.0"
    unist-util-is "^6.0.0"

mdast-util-to-markdown@^2.0.0:
  version "2.1.2"
  dependencies:
    "@types/mdast" "^4.0.0"
    "@types/unist" "^3.0.0"
    longest-streak "^3.0.0"
    mdast-util-phrasing "^4.0.0"
    mdast-util-to-string "^4.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-decode-string "^2.0.0"
    unist-util-visit "^5.0.0"
    zwitch "^2.0.0"

mdast-util-to-string@^4.0.0:
  version "4.0.0"
  dependencies:
    "@types/mdast" "^4.0.0"

mdn-data@2.0.28:
  version "2.0.28"

mdn-data@2.0.30:
  version "2.0.30"

media-typer@^1.1.0:
  version "1.1.0"

memory-fs@^0.5.0:
  version "0.5.0"
  dependencies:
    errno "^0.1.3"
    readable-stream "^2.0.1"

meow@^12.0.1:
  version "12.1.1"

merge-descriptors@^2.0.0:
  version "2.0.0"

merge-stream@^2.0.0:
  version "2.0.0"

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"

methods@^1.1.2:
  version "1.1.2"

micromark-core-commonmark@^2.0.0:
  version "2.0.3"
  dependencies:
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-factory-destination "^2.0.0"
    micromark-factory-label "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-factory-title "^2.0.0"
    micromark-factory-whitespace "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-html-tag-name "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-frontmatter@^2.0.0:
  version "2.0.0"
  dependencies:
    fault "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-autolink-literal@^2.0.0:
  version "2.1.0"
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-footnote@^2.0.0:
  version "2.1.0"
  dependencies:
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-strikethrough@^2.0.0:
  version "2.1.0"
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-classify-character "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-table@^2.0.0:
  version "2.1.1"
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm-tagfilter@^2.0.0:
  version "2.0.0"
  dependencies:
    micromark-util-types "^2.0.0"

micromark-extension-gfm-task-list-item@^2.0.0:
  version "2.1.0"
  dependencies:
    devlop "^1.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-extension-gfm@^3.0.0:
  version "3.0.0"
  dependencies:
    micromark-extension-gfm-autolink-literal "^2.0.0"
    micromark-extension-gfm-footnote "^2.0.0"
    micromark-extension-gfm-strikethrough "^2.0.0"
    micromark-extension-gfm-table "^2.0.0"
    micromark-extension-gfm-tagfilter "^2.0.0"
    micromark-extension-gfm-task-list-item "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-destination@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-label@^2.0.0:
  version "2.0.1"
  dependencies:
    devlop "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-space@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-title@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-factory-whitespace@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-character@^2.0.0:
  version "2.1.1"
  dependencies:
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-chunked@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-classify-character@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-combine-extensions@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-chunked "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-decode-numeric-character-reference@^2.0.0:
  version "2.0.2"
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-decode-string@^2.0.0:
  version "2.0.1"
  dependencies:
    decode-named-character-reference "^1.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-encode@^2.0.0:
  version "2.0.1"

micromark-util-html-tag-name@^2.0.0:
  version "2.0.1"

micromark-util-normalize-identifier@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-symbol "^2.0.0"

micromark-util-resolve-all@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-types "^2.0.0"

micromark-util-sanitize-uri@^2.0.0:
  version "2.0.1"
  dependencies:
    micromark-util-character "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-symbol "^2.0.0"

micromark-util-subtokenize@^2.0.0:
  version "2.1.0"
  dependencies:
    devlop "^1.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromark-util-symbol@^2.0.0:
  version "2.0.1"

micromark-util-types@^2.0.0:
  version "2.0.2"

micromark@^4.0.0:
  version "4.0.2"
  dependencies:
    "@types/debug" "^4.0.0"
    debug "^4.0.0"
    decode-named-character-reference "^1.0.0"
    devlop "^1.0.0"
    micromark-core-commonmark "^2.0.0"
    micromark-factory-space "^2.0.0"
    micromark-util-character "^2.0.0"
    micromark-util-chunked "^2.0.0"
    micromark-util-combine-extensions "^2.0.0"
    micromark-util-decode-numeric-character-reference "^2.0.0"
    micromark-util-encode "^2.0.0"
    micromark-util-normalize-identifier "^2.0.0"
    micromark-util-resolve-all "^2.0.0"
    micromark-util-sanitize-uri "^2.0.0"
    micromark-util-subtokenize "^2.0.0"
    micromark-util-symbol "^2.0.0"
    micromark-util-types "^2.0.0"

micromatch@^4.0.2, micromatch@^4.0.4, micromatch@^4.0.5, micromatch@~4.0.7:
  version "4.0.7"
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@^1.54.0:
  version "1.54.0"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

mime-types@^3.0.0, mime-types@^3.0.1:
  version "3.0.1"
  dependencies:
    mime-db "^1.54.0"

mime@^2.4.4:
  version "2.6.0"

mime@^3.0.0:
  version "3.0.0"

mime@^4.0.3:
  version "4.0.4"

mime@1.6.0:
  version "1.6.0"

mimic-fn@^2.1.0:
  version "2.1.0"

mimic-fn@^4.0.0:
  version "4.0.0"

min-document@^2.19.0:
  version "2.19.0"
  dependencies:
    dom-walk "^0.1.0"

min-indent@^1.0.0, min-indent@^1.0.1:
  version "1.0.1"

minimatch@^3.0.4:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.1.1:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^3.1.2:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^5.1.0:
  version "5.1.6"
  dependencies:
    brace-expansion "^2.0.1"

"minimatch@^9.0.3 || ^10.0.1":
  version "10.0.1"
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.3, minimatch@^9.0.4, minimatch@^9.0.5:
  version "9.0.5"
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.8:
  version "1.2.8"

minipass@^3.0.0:
  version "3.3.6"
  dependencies:
    yallist "^4.0.0"

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"

minipass@^5.0.0:
  version "5.0.0"

minizlib@^2.1.1:
  version "2.1.2"
  dependencies:
    minipass "^3.0.0"
    yallist "^4.0.0"

mitt@^3.0.1:
  version "3.0.1"

mkdirp@^1.0.3:
  version "1.0.4"

mlly@^1.2.0, mlly@^1.3.0, mlly@^1.4.2, mlly@^1.6.1, mlly@^1.7.0, mlly@^1.7.1:
  version "1.7.1"
  dependencies:
    acorn "^8.11.3"
    pathe "^1.1.2"
    pkg-types "^1.1.1"
    ufo "^1.5.3"

mlly@^1.7.4:
  version "1.7.4"
  dependencies:
    acorn "^8.14.0"
    pathe "^2.0.1"
    pkg-types "^1.3.0"
    ufo "^1.5.4"

mpd-parser@^1.2.2, mpd-parser@^1.3.0:
  version "1.3.0"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/vhs-utils" "^4.0.0"
    "@xmldom/xmldom" "^0.8.3"
    global "^4.4.0"

mri@^1.2.0:
  version "1.2.0"

mrmime@^1.0.0:
  version "1.0.1"

mrmime@^2.0.0:
  version "2.0.0"

ms@^2.1.1, ms@^2.1.3, ms@2.1.3:
  version "2.1.3"

ms@2.0.0:
  version "2.0.0"

ms@2.1.2:
  version "2.1.2"

mux.js@^7.0.1, mux.js@7.0.3:
  version "7.0.3"
  dependencies:
    "@babel/runtime" "^7.11.2"
    global "^4.4.0"

mz@^2.7.0:
  version "2.7.0"
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.4, nanoid@^3.3.7:
  version "3.3.7"

nanoid@^5.0.7:
  version "5.0.7"

napi-postinstall@^0.2.2:
  version "0.2.3"

napi-wasm@^1.1.0:
  version "1.1.0"

natural-compare@^1.4.0:
  version "1.4.0"

natural-orderby@^5.0.0:
  version "5.0.0"

negotiator@^1.0.0:
  version "1.0.0"

nitropack@^2.9.7:
  version "2.9.7"
  dependencies:
    "@cloudflare/kv-asset-handler" "^0.3.4"
    "@netlify/functions" "^2.8.0"
    "@rollup/plugin-alias" "^5.1.0"
    "@rollup/plugin-commonjs" "^25.0.8"
    "@rollup/plugin-inject" "^5.0.5"
    "@rollup/plugin-json" "^6.1.0"
    "@rollup/plugin-node-resolve" "^15.2.3"
    "@rollup/plugin-replace" "^5.0.7"
    "@rollup/plugin-terser" "^0.4.4"
    "@rollup/pluginutils" "^5.1.0"
    "@types/http-proxy" "^1.17.14"
    "@vercel/nft" "^0.26.5"
    archiver "^7.0.1"
    c12 "^1.11.1"
    chalk "^5.3.0"
    chokidar "^3.6.0"
    citty "^0.1.6"
    consola "^3.2.3"
    cookie-es "^1.1.0"
    croner "^8.0.2"
    crossws "^0.2.4"
    db0 "^0.1.4"
    defu "^6.1.4"
    destr "^2.0.3"
    dot-prop "^8.0.2"
    esbuild "^0.20.2"
    escape-string-regexp "^5.0.0"
    etag "^1.8.1"
    fs-extra "^11.2.0"
    globby "^14.0.1"
    gzip-size "^7.0.0"
    h3 "^1.12.0"
    hookable "^5.5.3"
    httpxy "^0.1.5"
    ioredis "^5.4.1"
    jiti "^1.21.6"
    klona "^2.0.6"
    knitwork "^1.1.0"
    listhen "^1.7.2"
    magic-string "^0.30.10"
    mime "^4.0.3"
    mlly "^1.7.1"
    mri "^1.2.0"
    node-fetch-native "^1.6.4"
    ofetch "^1.3.4"
    ohash "^1.1.3"
    openapi-typescript "^6.7.6"
    pathe "^1.1.2"
    perfect-debounce "^1.0.0"
    pkg-types "^1.1.1"
    pretty-bytes "^6.1.1"
    radix3 "^1.1.2"
    rollup "^4.18.0"
    rollup-plugin-visualizer "^5.12.0"
    scule "^1.3.0"
    semver "^7.6.2"
    serve-placeholder "^2.0.2"
    serve-static "^1.15.0"
    std-env "^3.7.0"
    ufo "^1.5.3"
    uncrypto "^0.1.3"
    unctx "^2.3.1"
    unenv "^1.9.0"
    unimport "^3.7.2"
    unstorage "^1.10.2"
    unwasm "^0.3.9"

node-addon-api@^7.0.0:
  version "7.1.1"

node-fetch-native@^1.6.1, node-fetch-native@^1.6.2, node-fetch-native@^1.6.3, node-fetch-native@^1.6.4:
  version "1.6.4"

node-fetch@^2.6.7:
  version "2.7.0"
  dependencies:
    whatwg-url "^5.0.0"

node-forge@^1.3.1:
  version "1.3.1"

node-gyp-build@^4.2.2:
  version "4.8.1"

node-releases@^2.0.14:
  version "2.0.14"

node-releases@^2.0.19:
  version "2.0.19"

nopt@^5.0.0:
  version "5.0.0"
  dependencies:
    abbrev "1"

normalize-package-data@^2.5.0:
  version "2.5.0"
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"

normalize-range@^0.1.2:
  version "0.1.2"

npm-run-path@^4.0.1:
  version "4.0.1"
  dependencies:
    path-key "^3.0.0"

npm-run-path@^5.1.0:
  version "5.3.0"
  dependencies:
    path-key "^4.0.0"

npmlog@^5.0.1:
  version "5.0.1"
  dependencies:
    are-we-there-yet "^2.0.0"
    console-control-strings "^1.1.0"
    gauge "^3.0.0"
    set-blocking "^2.0.0"

nth-check@^2.0.1, nth-check@^2.1.1:
  version "2.1.1"
  dependencies:
    boolbase "^1.0.0"

nuxi@^3.12.0:
  version "3.12.0"
  optionalDependencies:
    fsevents "~2.3.3"

nuxt-schema-org@^3.3.9:
  version "3.3.9"
  dependencies:
    "@nuxt/devtools-kit" "^1.3.9"
    "@nuxt/kit" "^3.12.3"
    "@unhead/schema-org" "^1.9.16"
    nuxt-site-config "^2.2.15"
    nuxt-site-config-kit "^2.2.15"
    pathe "^1.1.2"
    sirv "^2.0.4"

nuxt-site-config-kit@^2.2.15, nuxt-site-config-kit@2.2.15:
  version "2.2.15"
  dependencies:
    "@nuxt/kit" "^3.12.3"
    "@nuxt/schema" "^3.12.3"
    pkg-types "^1.1.3"
    site-config-stack "2.2.15"
    std-env "^3.7.0"
    ufo "^1.5.3"

nuxt-site-config@^2.2.15:
  version "2.2.15"
  dependencies:
    "@nuxt/devtools-kit" "^1.3.9"
    "@nuxt/kit" "^3.12.3"
    "@nuxt/schema" "^3.12.3"
    nuxt-site-config-kit "2.2.15"
    pathe "^1.1.2"
    pkg-types "^1.1.3"
    sirv "^2.0.4"
    site-config-stack "2.2.15"
    ufo "^1.5.3"

nuxt-viewport@2.1.5:
  version "2.1.5"
  dependencies:
    "@nuxt/kit" "npm:@nuxt/kit-edge@latest"
    bowser "^2.11.0"
    cookiejs "^2.1.3"
    vue-demi "^0.14.6"

nuxt-windicss@3.0.1:
  version "3.0.1"
  dependencies:
    "@nuxt/kit" "^3.12.3"
    "@windicss/config" "^1.9.3"
    "@windicss/plugin-utils" "^1.9.3"
    consola "^3.2.3"
    defu "^6.1.4"
    fs-extra "^11.2.0"
    h3 "^1.12.0"
    listhen "^1.7.2"
    pathe "^1.1.2"
    read-cache "^1.0.0"
    sirv "^2.0.4"
    vite-plugin-windicss "^1.9.3"
    windicss "^3.5.6"
    windicss-analysis "^0.3.5"
    windicss-webpack-plugin "^1.8.0"

nuxt@^3.12.3:
  version "3.12.3"
  dependencies:
    "@nuxt/devalue" "^2.0.2"
    "@nuxt/devtools" "^1.3.7"
    "@nuxt/kit" "3.12.3"
    "@nuxt/schema" "3.12.3"
    "@nuxt/telemetry" "^2.5.4"
    "@nuxt/vite-builder" "3.12.3"
    "@unhead/dom" "^1.9.14"
    "@unhead/ssr" "^1.9.14"
    "@unhead/vue" "^1.9.14"
    "@vue/shared" "^3.4.31"
    acorn "8.12.0"
    c12 "^1.11.1"
    chokidar "^3.6.0"
    compatx "^0.1.8"
    consola "^3.2.3"
    cookie-es "^1.1.0"
    defu "^6.1.4"
    destr "^2.0.3"
    devalue "^5.0.0"
    esbuild "^0.23.0"
    escape-string-regexp "^5.0.0"
    estree-walker "^3.0.3"
    globby "^14.0.2"
    h3 "^1.12.0"
    hookable "^5.5.3"
    ignore "^5.3.1"
    jiti "^1.21.6"
    klona "^2.0.6"
    knitwork "^1.1.0"
    magic-string "^0.30.10"
    mlly "^1.7.1"
    nitropack "^2.9.7"
    nuxi "^3.12.0"
    nypm "^0.3.9"
    ofetch "^1.3.4"
    ohash "^1.1.3"
    pathe "^1.1.2"
    perfect-debounce "^1.0.0"
    pkg-types "^1.1.2"
    radix3 "^1.1.2"
    scule "^1.3.0"
    semver "^7.6.2"
    std-env "^3.7.0"
    strip-literal "^2.1.0"
    ufo "^1.5.3"
    ultrahtml "^1.5.3"
    uncrypto "^0.1.3"
    unctx "^2.3.1"
    unenv "^1.9.0"
    unimport "^3.7.2"
    unplugin "^1.11.0"
    unplugin-vue-router "^0.10.0"
    unstorage "^1.10.2"
    untyped "^1.4.2"
    vue "^3.4.31"
    vue-bundle-renderer "^2.1.0"
    vue-devtools-stub "^0.1.0"
    vue-router "^4.4.0"

nypm@^0.3.8, nypm@^0.3.9:
  version "0.3.9"
  dependencies:
    citty "^0.1.6"
    consola "^3.2.3"
    execa "^8.0.1"
    pathe "^1.1.2"
    pkg-types "^1.1.1"
    ufo "^1.5.3"

object-assign@^4, object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"

object-inspect@^1.13.1:
  version "1.13.2"

object-inspect@^1.13.3:
  version "1.13.4"

oblivious-set@1.4.0:
  version "1.4.0"

ofetch@^1.3.3, ofetch@^1.3.4:
  version "1.3.4"
  dependencies:
    destr "^2.0.3"
    node-fetch-native "^1.6.3"
    ufo "^1.5.3"

ohash@^1.1.3:
  version "1.1.3"

on-finished@^2.4.1, on-finished@2.4.1:
  version "2.4.1"
  dependencies:
    ee-first "1.1.1"

on-finished@~2.3.0:
  version "2.3.0"
  dependencies:
    ee-first "1.1.1"

once@^1.3.0, once@^1.4.0:
  version "1.4.0"
  dependencies:
    wrappy "1"

one-time@^1.0.0:
  version "1.0.0"
  dependencies:
    fn.name "1.x.x"

onetime@^5.1.0:
  version "5.1.2"
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  dependencies:
    mimic-fn "^4.0.0"

open@^10.1.0:
  version "10.1.0"
  dependencies:
    default-browser "^5.2.1"
    define-lazy-prop "^3.0.0"
    is-inside-container "^1.0.0"
    is-wsl "^3.1.0"

open@^8.4.0:
  version "8.4.2"
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

openapi-typescript@^6.7.6:
  version "6.7.6"
  dependencies:
    ansi-colors "^4.1.3"
    fast-glob "^3.3.2"
    js-yaml "^4.1.0"
    supports-color "^9.4.0"
    undici "^5.28.4"
    yargs-parser "^21.1.1"

optionator@^0.9.3:
  version "0.9.4"
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

p-finally@^1.0.0:
  version "1.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2:
  version "3.1.0"
  dependencies:
    yocto-queue "^0.1.0"

p-limit@^4.0.0:
  version "4.0.0"
  dependencies:
    yocto-queue "^1.0.0"

p-locate@^4.1.0:
  version "4.1.0"
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  dependencies:
    p-limit "^3.0.2"

p-locate@^6.0.0:
  version "6.0.0"
  dependencies:
    p-limit "^4.0.0"

p-queue@6.6.2:
  version "6.6.2"
  dependencies:
    eventemitter3 "^4.0.4"
    p-timeout "^3.2.0"

p-timeout@^3.2.0:
  version "3.2.0"
  dependencies:
    p-finally "^1.0.0"

p-try@^2.0.0:
  version "2.2.0"

package-json-from-dist@^1.0.0:
  version "1.0.0"

package-manager-detector@^1.3.0:
  version "1.3.0"

parent-module@^1.0.0:
  version "1.0.1"
  dependencies:
    callsites "^3.0.0"

parse-git-config@^3.0.0:
  version "3.0.0"
  dependencies:
    git-config-path "^2.0.0"
    ini "^1.3.5"

parse-gitignore@^2.0.0:
  version "2.0.0"

parse-imports-exports@^0.2.4:
  version "0.2.4"
  dependencies:
    parse-statements "1.0.11"

parse-imports@^2.1.1:
  version "2.1.1"
  dependencies:
    es-module-lexer "^1.5.3"
    slashes "^3.0.12"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

parse-path@^7.0.0:
  version "7.0.0"
  dependencies:
    protocols "^2.0.0"

parse-statements@1.0.11:
  version "1.0.11"

parse-url@^8.1.0:
  version "8.1.0"
  dependencies:
    parse-path "^7.0.0"

parse5-htmlparser2-tree-adapter@^7.0.0:
  version "7.0.0"
  dependencies:
    domhandler "^5.0.2"
    parse5 "^7.0.0"

parse5@^7.0.0:
  version "7.1.2"
  dependencies:
    entities "^4.4.0"

parseurl@^1.3.3, parseurl@~1.3.3:
  version "1.3.3"

path-exists@^4.0.0:
  version "4.0.0"

path-exists@^5.0.0:
  version "5.0.0"

path-is-absolute@^1.0.0:
  version "1.0.1"

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"

path-key@^4.0.0:
  version "4.0.0"

path-parse@^1.0.7:
  version "1.0.7"

path-scurry@^1.11.1:
  version "1.11.1"
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@^8.0.0:
  version "8.2.0"

path-type@^4.0.0:
  version "4.0.0"

path-type@^5.0.0:
  version "5.0.0"

pathe@^1.0.0, pathe@^1.1.0, pathe@^1.1.1, pathe@^1.1.2:
  version "1.1.2"

pathe@^2.0.1, pathe@^2.0.2, pathe@^2.0.3:
  version "2.0.3"

perfect-debounce@^1.0.0:
  version "1.0.0"

picocolors@^1.0.0, picocolors@^1.0.1:
  version "1.0.1"

picocolors@^1.1.1:
  version "1.1.1"

picomatch@^2.0.4:
  version "2.3.1"

picomatch@^2.2.1:
  version "2.3.1"

picomatch@^2.2.2:
  version "2.3.1"

picomatch@^2.3.1:
  version "2.3.1"

picomatch@^4.0.2:
  version "4.0.2"

pidtree@~0.6.0:
  version "0.6.0"

pify@^2.3.0:
  version "2.3.0"

pinia-plugin-persistedstate@>=3.2.1:
  version "3.2.1"

pinia-shared-state@^0.5.1:
  version "0.5.1"
  dependencies:
    broadcast-channel "^7.0.0"
    vue-demi "^0.14.6"

pinia@^2.1.7, pinia@>=2.1.7:
  version "2.1.7"
  dependencies:
    "@vue/devtools-api" "^6.5.0"
    vue-demi ">=0.14.5"

pirates@^4.0.1:
  version "4.0.6"

pkce-challenge@^5.0.0:
  version "5.0.0"

pkcs7@^1.0.4:
  version "1.0.4"
  dependencies:
    "@babel/runtime" "^7.5.5"

pkg-types@^1.0.3, pkg-types@^1.1.1, pkg-types@^1.1.2, pkg-types@^1.1.3:
  version "1.1.3"
  dependencies:
    confbox "^0.1.7"
    mlly "^1.7.1"
    pathe "^1.1.2"

pkg-types@^1.3.0:
  version "1.3.1"
  dependencies:
    confbox "^0.1.8"
    mlly "^1.7.4"
    pathe "^2.0.1"

pkg-types@^2.0.1:
  version "2.1.0"
  dependencies:
    confbox "^0.2.1"
    exsolve "^1.0.1"
    pathe "^2.0.3"

pluralize@^8.0.0:
  version "8.0.0"

pnpm-workspace-yaml@0.3.1:
  version "0.3.1"
  dependencies:
    yaml "^2.7.0"

postcss-calc@^10.0.0:
  version "10.0.0"
  dependencies:
    postcss-selector-parser "^6.0.16"
    postcss-value-parser "^4.2.0"

postcss-colormin@^7.0.1:
  version "7.0.1"
  dependencies:
    browserslist "^4.23.1"
    caniuse-api "^3.0.0"
    colord "^2.9.3"
    postcss-value-parser "^4.2.0"

postcss-convert-values@^7.0.2:
  version "7.0.2"
  dependencies:
    browserslist "^4.23.1"
    postcss-value-parser "^4.2.0"

postcss-discard-comments@^7.0.1:
  version "7.0.1"
  dependencies:
    postcss-selector-parser "^6.1.0"

postcss-discard-duplicates@^7.0.0:
  version "7.0.0"

postcss-discard-empty@^7.0.0:
  version "7.0.0"

postcss-discard-overridden@^7.0.0:
  version "7.0.0"

postcss-import-resolver@^2.0.0:
  version "2.0.0"
  dependencies:
    enhanced-resolve "^4.1.1"

postcss-merge-longhand@^7.0.2:
  version "7.0.2"
  dependencies:
    postcss-value-parser "^4.2.0"
    stylehacks "^7.0.2"

postcss-merge-rules@^7.0.2:
  version "7.0.2"
  dependencies:
    browserslist "^4.23.1"
    caniuse-api "^3.0.0"
    cssnano-utils "^5.0.0"
    postcss-selector-parser "^6.1.0"

postcss-minify-font-values@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-minify-gradients@^7.0.0:
  version "7.0.0"
  dependencies:
    colord "^2.9.3"
    cssnano-utils "^5.0.0"
    postcss-value-parser "^4.2.0"

postcss-minify-params@^7.0.1:
  version "7.0.1"
  dependencies:
    browserslist "^4.23.1"
    cssnano-utils "^5.0.0"
    postcss-value-parser "^4.2.0"

postcss-minify-selectors@^7.0.2:
  version "7.0.2"
  dependencies:
    cssesc "^3.0.0"
    postcss-selector-parser "^6.1.0"

postcss-normalize-charset@^7.0.0:
  version "7.0.0"

postcss-normalize-display-values@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-positions@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-repeat-style@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-string@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-timing-functions@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-unicode@^7.0.1:
  version "7.0.1"
  dependencies:
    browserslist "^4.23.1"
    postcss-value-parser "^4.2.0"

postcss-normalize-url@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-normalize-whitespace@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-ordered-values@^7.0.1:
  version "7.0.1"
  dependencies:
    cssnano-utils "^5.0.0"
    postcss-value-parser "^4.2.0"

postcss-reduce-initial@^7.0.1:
  version "7.0.1"
  dependencies:
    browserslist "^4.23.1"
    caniuse-api "^3.0.0"

postcss-reduce-transforms@^7.0.0:
  version "7.0.0"
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-selector-parser@^6.0.15, postcss-selector-parser@^6.0.16, postcss-selector-parser@^6.1.0:
  version "6.1.1"
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-svgo@^7.0.1:
  version "7.0.1"
  dependencies:
    postcss-value-parser "^4.2.0"
    svgo "^3.3.2"

postcss-unique-selectors@^7.0.1:
  version "7.0.1"
  dependencies:
    postcss-selector-parser "^6.1.0"

postcss-value-parser@^4.2.0:
  version "4.2.0"

postcss@^8.4.14, postcss@^8.4.38, postcss@^8.4.39, postcss@8.4.39:
  version "8.4.39"
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.0.1"
    source-map-js "^1.2.0"

prelude-ls@^1.2.1:
  version "1.2.1"

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  dependencies:
    fast-diff "^1.1.2"

"prettier@^1.18.2 || ^2.0.0":
  version "2.8.8"

prettier@^3.4.2:
  version "3.5.3"

prettier@3.3.3:
  version "3.3.3"

pretty-bytes@^6.1.1:
  version "6.1.1"

process-nextick-args@~2.0.0:
  version "2.0.1"

process@^0.11.10:
  version "0.11.10"

prompts@^2.4.2:
  version "2.4.2"
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

protocols@^2.0.0, protocols@^2.0.1:
  version "2.0.1"

proxy-addr@^2.0.7:
  version "2.0.7"
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

prr@~1.0.1:
  version "1.0.1"

punycode@^2.1.0:
  version "2.3.1"

qs@^6.12.3, qs@^6.9.1:
  version "6.12.3"
  dependencies:
    side-channel "^1.0.6"

qs@^6.14.0:
  version "6.14.0"
  dependencies:
    side-channel "^1.1.0"

quansync@^0.2.8:
  version "0.2.10"

queue-microtask@^1.2.2:
  version "1.2.3"

queue-tick@^1.0.1:
  version "1.0.1"

radix3@^1.1.2:
  version "1.1.2"

randombytes@^2.1.0:
  version "2.1.0"
  dependencies:
    safe-buffer "^5.1.0"

range-parser@^1.2.1, range-parser@~1.2.1:
  version "1.2.1"

raw-body@^3.0.0:
  version "3.0.0"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.6.3"
    unpipe "1.0.0"

rc9@^2.1.2:
  version "2.1.2"
  dependencies:
    defu "^6.1.4"
    destr "^2.0.3"

read-cache@^1.0.0:
  version "1.0.0"
  dependencies:
    pify "^2.3.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

readable-stream@^2.0.1:
  version "2.3.8"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.0.5:
  version "2.3.8"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^2.3.6:
  version "2.3.8"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@^3.4.0:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.6.0:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^3.6.2:
  version "3.6.2"
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readable-stream@^4.0.0:
  version "4.5.2"
  dependencies:
    abort-controller "^3.0.0"
    buffer "^6.0.3"
    events "^3.3.0"
    process "^0.11.10"
    string_decoder "^1.3.0"

readdir-glob@^1.1.2:
  version "1.1.3"
  dependencies:
    minimatch "^5.1.0"

readdirp@~3.6.0:
  version "3.6.0"
  dependencies:
    picomatch "^2.2.1"

redis-errors@^1.0.0, redis-errors@^1.2.0:
  version "1.2.0"

redis-parser@^3.0.0:
  version "3.0.0"
  dependencies:
    redis-errors "^1.0.0"

refa@^0.12.0, refa@^0.12.1:
  version "0.12.1"
  dependencies:
    "@eslint-community/regexpp" "^4.8.0"

regenerator-runtime@^0.14.0:
  version "0.14.1"

regexp-ast-analysis@^0.7.0, regexp-ast-analysis@^0.7.1:
  version "0.7.1"
  dependencies:
    "@eslint-community/regexpp" "^4.8.0"
    refa "^0.12.1"

regexp-tree@^0.1.27:
  version "0.1.27"

regjsparser@^0.10.0:
  version "0.10.0"
  dependencies:
    jsesc "~0.5.0"

regjsparser@^0.12.0:
  version "0.12.0"
  dependencies:
    jsesc "~3.0.2"

require-directory@^2.1.1:
  version "2.1.1"

require-from-string@^2.0.2:
  version "2.0.2"

resolve-from@^4.0.0:
  version "4.0.0"

resolve-from@^5.0.0:
  version "5.0.0"

resolve-pkg-maps@^1.0.0:
  version "1.0.0"

resolve@^1.10.0, resolve@^1.22.1, resolve@^1.22.4:
  version "1.22.8"
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^4.0.0:
  version "4.0.0"
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.0.4"

rfdc@^1.4.1:
  version "1.4.1"

rimraf@^3.0.2:
  version "3.0.2"
  dependencies:
    glob "^7.1.3"

rollup-plugin-visualizer@^5.12.0:
  version "5.12.0"
  dependencies:
    open "^8.4.0"
    picomatch "^2.3.1"
    source-map "^0.7.4"
    yargs "^17.5.1"

rollup@^4.13.0, rollup@^4.18.0:
  version "4.18.1"
  dependencies:
    "@types/estree" "1.0.5"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.18.1"
    "@rollup/rollup-android-arm64" "4.18.1"
    "@rollup/rollup-darwin-arm64" "4.18.1"
    "@rollup/rollup-darwin-x64" "4.18.1"
    "@rollup/rollup-linux-arm-gnueabihf" "4.18.1"
    "@rollup/rollup-linux-arm-musleabihf" "4.18.1"
    "@rollup/rollup-linux-arm64-gnu" "4.18.1"
    "@rollup/rollup-linux-arm64-musl" "4.18.1"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.18.1"
    "@rollup/rollup-linux-riscv64-gnu" "4.18.1"
    "@rollup/rollup-linux-s390x-gnu" "4.18.1"
    "@rollup/rollup-linux-x64-gnu" "4.18.1"
    "@rollup/rollup-linux-x64-musl" "4.18.1"
    "@rollup/rollup-win32-arm64-msvc" "4.18.1"
    "@rollup/rollup-win32-ia32-msvc" "4.18.1"
    "@rollup/rollup-win32-x64-msvc" "4.18.1"
    fsevents "~2.3.2"

router@^2.2.0:
  version "2.2.0"
  dependencies:
    debug "^4.4.0"
    depd "^2.0.0"
    is-promise "^4.0.0"
    parseurl "^1.3.3"
    path-to-regexp "^8.0.0"

run-applescript@^7.0.0:
  version "7.0.0"

run-parallel@^1.1.9:
  version "1.2.0"
  dependencies:
    queue-microtask "^1.2.2"

safe-buffer@^5.1.0, safe-buffer@~5.2.0, safe-buffer@5.2.1:
  version "5.2.1"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"

safe-stable-stringify@^2.3.1:
  version "2.4.3"

"safer-buffer@>= 2.1.2 < 3.0.0":
  version "2.1.2"

sass@1.77.8:
  version "1.77.8"
  dependencies:
    chokidar ">=3.0.0 <4.0.0"
    immutable "^4.0.0"
    source-map-js ">=0.6.2 <2.0.0"

scslre@^0.3.0:
  version "0.3.0"
  dependencies:
    "@eslint-community/regexpp" "^4.8.0"
    refa "^0.12.0"
    regexp-ast-analysis "^0.7.0"

scule@^1.0.0, scule@^1.1.1, scule@^1.2.0, scule@^1.3.0:
  version "1.3.0"

semver@^6.0.0:
  version "6.3.1"

semver@^6.3.0:
  version "6.3.1"

semver@^6.3.1:
  version "6.3.1"

semver@^7.3.4, semver@^7.3.5, semver@^7.5.4, semver@^7.6.2:
  version "7.6.2"

semver@^7.3.6:
  version "7.6.3"

semver@^7.6.0:
  version "7.6.3"

semver@^7.6.1:
  version "7.6.3"

semver@^7.6.3:
  version "7.7.1"

semver@^7.7.1:
  version "7.7.1"

"semver@2 || 3 || 4 || 5":
  version "5.7.2"

send@^1.1.0, send@^1.2.0:
  version "1.2.0"
  dependencies:
    debug "^4.3.5"
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    etag "^1.8.1"
    fresh "^2.0.0"
    http-errors "^2.0.0"
    mime-types "^3.0.1"
    ms "^2.1.3"
    on-finished "^2.4.1"
    range-parser "^1.2.1"
    statuses "^2.0.1"

send@0.18.0:
  version "0.18.0"
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serialize-javascript@^6.0.1:
  version "6.0.2"
  dependencies:
    randombytes "^2.1.0"

serve-placeholder@^2.0.2:
  version "2.0.2"
  dependencies:
    defu "^6.1.4"

serve-static@^1.15.0:
  version "1.15.0"
  dependencies:
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.18.0"

serve-static@^2.2.0:
  version "2.2.0"
  dependencies:
    encodeurl "^2.0.0"
    escape-html "^1.0.3"
    parseurl "^1.3.3"
    send "^1.2.0"

set-blocking@^2.0.0:
  version "2.0.0"

set-function-length@^1.2.1:
  version "1.2.2"
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

setprototypeof@1.2.0:
  version "1.2.0"

shebang-command@^2.0.0:
  version "2.0.0"
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"

shell-quote@^1.8.1:
  version "1.8.1"

side-channel-list@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6:
  version "1.0.6"
  dependencies:
    call-bind "^1.0.7"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.4"
    object-inspect "^1.13.1"

side-channel@^1.1.0:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.0, signal-exit@^3.0.2, signal-exit@^3.0.7:
  version "3.0.7"

signal-exit@^4.0.1:
  version "4.1.0"

signal-exit@^4.1.0:
  version "4.1.0"

simple-git@^3.25.0:
  version "3.25.0"
  dependencies:
    "@kwsites/file-exists" "^1.1.1"
    "@kwsites/promise-deferred" "^1.1.1"
    debug "^4.3.5"

simple-swizzle@^0.2.2:
  version "0.2.2"
  dependencies:
    is-arrayish "^0.3.1"

sirv@^1.0.12:
  version "1.0.19"
  dependencies:
    "@polka/url" "^1.0.0-next.20"
    mrmime "^1.0.0"
    totalist "^1.0.0"

sirv@^2.0.4:
  version "2.0.4"
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

sisteransi@^1.0.5:
  version "1.0.5"

site-config-stack@2.2.15:
  version "2.2.15"
  dependencies:
    ufo "^1.5.3"

slash@^3.0.0:
  version "3.0.0"

slash@^4.0.0:
  version "4.0.0"

slash@^5.1.0:
  version "5.1.0"

slashes@^3.0.12:
  version "3.0.12"

slice-ansi@^5.0.0:
  version "5.0.0"
  dependencies:
    ansi-styles "^6.0.0"
    is-fullwidth-code-point "^4.0.0"

slice-ansi@^7.0.0:
  version "7.1.0"
  dependencies:
    ansi-styles "^6.2.1"
    is-fullwidth-code-point "^5.0.0"

smob@^1.0.0:
  version "1.5.0"

source-map-js@^1.0.1, source-map-js@^1.0.2, source-map-js@^1.2.0, "source-map-js@>=0.6.2 <2.0.0":
  version "1.2.0"

source-map-support@~0.5.20:
  version "0.5.21"
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"

source-map@^0.7.4:
  version "0.7.4"

spdx-correct@^3.0.0:
  version "3.2.0"
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-expression-parse@^4.0.0:
  version "4.0.0"
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.18"

speakingurl@^14.0.1:
  version "14.0.1"

split2@^4.0.0:
  version "4.2.0"

stable-hash@^0.0.4:
  version "0.0.4"

stable-hash@^0.0.5:
  version "0.0.5"

stack-trace@0.0.x:
  version "0.0.10"

standard-as-callback@^2.1.0:
  version "2.1.0"

statuses@^2.0.1, statuses@2.0.1:
  version "2.0.1"

statuses@~1.5.0:
  version "1.5.0"

std-env@^3.4.3, std-env@^3.7.0:
  version "3.7.0"

streamx@^2.15.0:
  version "2.18.0"
  dependencies:
    fast-fifo "^1.3.2"
    queue-tick "^1.0.1"
    text-decoder "^1.1.0"
  optionalDependencies:
    bare-events "^2.2.0"

string_decoder@^1.1.1, string_decoder@^1.3.0:
  version "1.3.0"
  dependencies:
    safe-buffer "~5.2.0"

string_decoder@~1.1.1:
  version "1.1.1"
  dependencies:
    safe-buffer "~5.1.0"

string-argv@~0.3.2:
  version "0.3.2"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

"string-width@^1.0.2 || 2 || 3 || 4", string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.3:
  version "4.2.3"
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string-width@^7.0.0:
  version "7.2.0"
  dependencies:
    emoji-regex "^10.3.0"
    get-east-asian-width "^1.0.0"
    strip-ansi "^7.1.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1, strip-ansi@^7.1.0:
  version "7.1.0"
  dependencies:
    ansi-regex "^6.0.1"

strip-final-newline@^3.0.0:
  version "3.0.0"

strip-indent@^3.0.0:
  version "3.0.0"
  dependencies:
    min-indent "^1.0.0"

strip-indent@^4.0.0:
  version "4.0.0"
  dependencies:
    min-indent "^1.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"

strip-literal@^2.1.0:
  version "2.1.0"
  dependencies:
    js-tokens "^9.0.0"

stylehacks@^7.0.2:
  version "7.0.2"
  dependencies:
    browserslist "^4.23.1"
    postcss-selector-parser "^6.1.0"

sucrase@^3.34.0:
  version "3.35.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

superagent@5.2.2:
  version "5.2.2"
  dependencies:
    component-emitter "^1.3.0"
    cookiejar "^2.1.2"
    debug "^4.1.1"
    fast-safe-stringify "^2.0.7"
    form-data "^3.0.0"
    formidable "^1.2.1"
    methods "^1.1.2"
    mime "^2.4.4"
    qs "^6.9.1"
    readable-stream "^3.4.0"
    semver "^6.3.0"

superjson@^2.2.1:
  version "2.2.1"
  dependencies:
    copy-anything "^3.0.2"

supports-color@^5.3.0:
  version "5.5.0"
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.1.0:
  version "7.2.0"
  dependencies:
    has-flag "^4.0.0"

supports-color@^9.4.0:
  version "9.4.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"

svg-tags@^1.0.0:
  version "1.0.0"

svgo@^3.3.2:
  version "3.3.2"
  dependencies:
    "@trysound/sax" "0.2.0"
    commander "^7.2.0"
    css-select "^5.1.0"
    css-tree "^2.3.1"
    css-what "^6.1.0"
    csso "^5.0.5"
    picocolors "^1.0.0"

"synckit@^0.6.2 || ^0.7.3 || ^0.10.3":
  version "0.10.3"
  dependencies:
    "@pkgr/core" "^0.2.0"
    tslib "^2.8.1"

synckit@^0.9.0:
  version "0.9.1"
  dependencies:
    "@pkgr/core" "^0.1.0"
    tslib "^2.6.2"

synckit@^0.9.2:
  version "0.9.2"
  dependencies:
    "@pkgr/core" "^0.1.0"
    tslib "^2.6.2"

system-architecture@^0.1.0:
  version "0.1.0"

tapable@^1.0.0:
  version "1.1.3"

tapable@^2.2.0:
  version "2.2.1"

tar-stream@^3.0.0:
  version "3.1.7"
  dependencies:
    b4a "^1.6.4"
    fast-fifo "^1.2.0"
    streamx "^2.15.0"

tar@^6.1.11, tar@^6.2.0:
  version "6.2.1"
  dependencies:
    chownr "^2.0.0"
    fs-minipass "^2.0.0"
    minipass "^5.0.0"
    minizlib "^2.1.1"
    mkdirp "^1.0.3"
    yallist "^4.0.0"

terser@^5.17.4:
  version "5.31.2"
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-decoder@^1.1.0:
  version "1.1.1"
  dependencies:
    b4a "^1.6.4"

text-extensions@^2.0.0:
  version "2.4.0"

text-hex@1.0.x:
  version "1.0.0"

thenify-all@^1.0.0:
  version "1.6.0"
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  dependencies:
    any-promise "^1.0.0"

three@^0.126.1:
  version "0.126.1"
  resolved "https://registry.npmjs.org/three/-/three-0.126.1.tgz"
  integrity sha512-eOEXnZeE1FDV0XgL1u08auIP13jxdN9LQBAEmlErYzMxtIIfuGIAZbijOyookALUhqVzVOx0Tywj6n192VM+nQ==

"through@>=2.2.7 <3":
  version "2.3.8"

tiny-invariant@^1.1.0:
  version "1.3.3"

tinyexec@^1.0.1:
  version "1.0.1"

tinyglobby@^0.2.12:
  version "0.2.13"
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

to-fast-properties@^2.0.0:
  version "2.0.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"

toml-eslint-parser@^0.10.0:
  version "0.10.0"
  dependencies:
    eslint-visitor-keys "^3.0.0"

tosource@^2.0.0-alpha.3:
  version "2.0.0-alpha.3"

totalist@^1.0.0:
  version "1.1.0"

totalist@^3.0.0:
  version "3.0.1"

tr46@~0.0.3:
  version "0.0.3"

translate@3.0.0:
  version "3.0.0"

triple-beam@^1.2.0, triple-beam@^1.3.0:
  version "1.4.1"

ts-api-utils@^1.3.0:
  version "1.3.0"

ts-api-utils@^2.1.0:
  version "2.1.0"

ts-interface-checker@^0.1.9:
  version "0.1.13"

tslib@^2.4.1, tslib@^2.6.2:
  version "2.6.3"

tslib@^2.8.1:
  version "2.8.1"

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"

type-fest@^0.21.3:
  version "0.21.3"

type-fest@^0.6.0:
  version "0.6.0"

type-fest@^0.8.1:
  version "0.8.1"

type-fest@^3.8.0:
  version "3.13.1"

type-fest@^4.18.3:
  version "4.22.0"

type-is@^2.0.0, type-is@^2.0.1:
  version "2.0.1"
  dependencies:
    content-type "^1.0.5"
    media-typer "^1.1.0"
    mime-types "^3.0.0"

typescript@^5.5.3:
  version "5.5.3"

ua-parser-js@^1.0.34:
  version "1.0.38"

ufo@^1.1.2, ufo@^1.3.1, ufo@^1.4.0, ufo@^1.5.3:
  version "1.5.3"

ufo@^1.5.4:
  version "1.6.1"

ultrahtml@^1.5.3:
  version "1.5.3"

uncrypto@^0.1.3:
  version "0.1.3"

unctx@^2.3.1:
  version "2.3.1"
  dependencies:
    acorn "^8.8.2"
    estree-walker "^3.0.3"
    magic-string "^0.30.0"
    unplugin "^1.3.1"

undici-types@~5.26.4:
  version "5.26.5"

undici@^5.28.4:
  version "5.28.4"
  dependencies:
    "@fastify/busboy" "^2.0.0"

unenv@^1.9.0:
  version "1.9.0"
  dependencies:
    consola "^3.2.3"
    defu "^6.1.3"
    mime "^3.0.0"
    node-fetch-native "^1.6.1"
    pathe "^1.1.1"

unhead@1.9.15:
  version "1.9.15"
  dependencies:
    "@unhead/dom" "1.9.15"
    "@unhead/schema" "1.9.15"
    "@unhead/shared" "1.9.15"
    hookable "^5.5.3"

unicorn-magic@^0.1.0:
  version "0.1.0"

unimport@^3.4.0, unimport@^3.7.2:
  version "3.7.2"
  dependencies:
    "@rollup/pluginutils" "^5.1.0"
    acorn "^8.11.3"
    escape-string-regexp "^5.0.0"
    estree-walker "^3.0.3"
    fast-glob "^3.3.2"
    local-pkg "^0.5.0"
    magic-string "^0.30.10"
    mlly "^1.7.0"
    pathe "^1.1.2"
    pkg-types "^1.1.1"
    scule "^1.3.0"
    strip-literal "^2.1.0"
    unplugin "^1.10.1"

unimport@^3.7.1:
  version "3.9.0"
  dependencies:
    "@rollup/pluginutils" "^5.1.0"
    acorn "^8.12.1"
    escape-string-regexp "^5.0.0"
    estree-walker "^3.0.3"
    fast-glob "^3.3.2"
    local-pkg "^0.5.0"
    magic-string "^0.30.10"
    mlly "^1.7.1"
    pathe "^1.1.2"
    pkg-types "^1.1.3"
    scule "^1.3.0"
    strip-literal "^2.1.0"
    unplugin "^1.11.0"

unist-util-is@^6.0.0:
  version "6.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-stringify-position@^4.0.0:
  version "4.0.0"
  dependencies:
    "@types/unist" "^3.0.0"

unist-util-visit-parents@^6.0.0:
  version "6.0.1"
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"

unist-util-visit@^5.0.0:
  version "5.0.0"
  dependencies:
    "@types/unist" "^3.0.0"
    unist-util-is "^6.0.0"
    unist-util-visit-parents "^6.0.0"

universalify@^2.0.0:
  version "2.0.1"

unload@2.4.1:
  version "2.4.1"

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"

unplugin-vue-router@^0.10.0:
  version "0.10.0"
  dependencies:
    "@babel/types" "^7.24.7"
    "@rollup/pluginutils" "^5.1.0"
    "@vue-macros/common" "^1.10.4"
    ast-walker-scope "^0.6.1"
    chokidar "^3.6.0"
    fast-glob "^3.3.2"
    json5 "^2.2.3"
    local-pkg "^0.5.0"
    mlly "^1.7.1"
    pathe "^1.1.2"
    scule "^1.3.0"
    unplugin "^1.10.1"
    yaml "^2.4.5"

unplugin@^1.1.0, unplugin@^1.10.0, unplugin@^1.10.1, unplugin@^1.11.0, unplugin@^1.3.1, unplugin@^1.5.0:
  version "1.11.0"
  dependencies:
    acorn "^8.11.3"
    chokidar "^3.6.0"
    webpack-sources "^3.2.3"
    webpack-virtual-modules "^0.6.1"

unrs-resolver@^1.7.0:
  version "1.7.2"
  dependencies:
    napi-postinstall "^0.2.2"
  optionalDependencies:
    "@unrs/resolver-binding-darwin-arm64" "1.7.2"
    "@unrs/resolver-binding-darwin-x64" "1.7.2"
    "@unrs/resolver-binding-freebsd-x64" "1.7.2"
    "@unrs/resolver-binding-linux-arm-gnueabihf" "1.7.2"
    "@unrs/resolver-binding-linux-arm-musleabihf" "1.7.2"
    "@unrs/resolver-binding-linux-arm64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-arm64-musl" "1.7.2"
    "@unrs/resolver-binding-linux-ppc64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-riscv64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-riscv64-musl" "1.7.2"
    "@unrs/resolver-binding-linux-s390x-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-x64-gnu" "1.7.2"
    "@unrs/resolver-binding-linux-x64-musl" "1.7.2"
    "@unrs/resolver-binding-wasm32-wasi" "1.7.2"
    "@unrs/resolver-binding-win32-arm64-msvc" "1.7.2"
    "@unrs/resolver-binding-win32-ia32-msvc" "1.7.2"
    "@unrs/resolver-binding-win32-x64-msvc" "1.7.2"

unstorage@^1.10.2:
  version "1.10.2"
  dependencies:
    anymatch "^3.1.3"
    chokidar "^3.6.0"
    destr "^2.0.3"
    h3 "^1.11.1"
    listhen "^1.7.2"
    lru-cache "^10.2.0"
    mri "^1.2.0"
    node-fetch-native "^1.6.2"
    ofetch "^1.3.3"
    ufo "^1.4.0"

untun@^0.1.3:
  version "0.1.3"
  dependencies:
    citty "^0.1.5"
    consola "^3.2.3"
    pathe "^1.1.1"

untyped@^1.4.0, untyped@^1.4.2:
  version "1.4.2"
  dependencies:
    "@babel/core" "^7.23.7"
    "@babel/standalone" "^7.23.8"
    "@babel/types" "^7.23.6"
    defu "^6.1.4"
    jiti "^1.21.0"
    mri "^1.2.0"
    scule "^1.2.0"

unwasm@^0.3.9:
  version "0.3.9"
  dependencies:
    knitwork "^1.0.0"
    magic-string "^0.30.8"
    mlly "^1.6.1"
    pathe "^1.1.2"
    pkg-types "^1.0.3"
    unplugin "^1.10.0"

update-browserslist-db@^1.1.0:
  version "1.1.0"
  dependencies:
    escalade "^3.1.2"
    picocolors "^1.0.1"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uqr@^0.1.2:
  version "0.1.2"

uri-js@^4.2.2:
  version "4.4.1"
  dependencies:
    punycode "^2.1.0"

url-toolkit@^2.2.1:
  version "2.2.5"

urlpattern-polyfill@8.0.2:
  version "8.0.2"

util-deprecate@^1.0.1, util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"

utils-merge@1.0.1:
  version "1.0.1"

uuid@^9.0.0:
  version "9.0.1"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

vary@^1, vary@^1.1.2:
  version "1.1.2"

"video.js@^7 || ^8", video.js@8.17.4:
  version "8.17.4"
  dependencies:
    "@babel/runtime" "^7.12.5"
    "@videojs/http-streaming" "3.13.3"
    "@videojs/vhs-utils" "^4.0.0"
    "@videojs/xhr" "2.7.0"
    aes-decrypter "^4.0.1"
    global "4.4.0"
    m3u8-parser "^7.1.0"
    mpd-parser "^1.2.2"
    mux.js "^7.0.1"
    videojs-contrib-quality-levels "4.1.0"
    videojs-font "4.2.0"
    videojs-vtt.js "0.15.5"

videojs-contrib-quality-levels@4.1.0:
  version "4.1.0"
  dependencies:
    global "^4.4.0"

videojs-font@4.2.0:
  version "4.2.0"

videojs-vtt.js@0.15.5:
  version "0.15.5"
  dependencies:
    global "^4.3.1"

vite-hot-client@^0.2.3:
  version "0.2.3"

vite-node@^1.6.0:
  version "1.6.0"
  dependencies:
    cac "^6.7.14"
    debug "^4.3.4"
    pathe "^1.1.1"
    picocolors "^1.0.0"
    vite "^5.0.0"

vite-plugin-checker@^0.7.0:
  version "0.7.1"
  dependencies:
    "@babel/code-frame" "^7.12.13"
    ansi-escapes "^4.3.0"
    chalk "^4.1.1"
    chokidar "^3.5.1"
    commander "^8.0.0"
    fast-glob "^3.2.7"
    fs-extra "^11.1.0"
    npm-run-path "^4.0.1"
    strip-ansi "^6.0.0"
    tiny-invariant "^1.1.0"
    vscode-languageclient "^7.0.0"
    vscode-languageserver "^7.0.0"
    vscode-languageserver-textdocument "^1.0.1"
    vscode-uri "^3.0.2"

vite-plugin-inspect@^0.8.4:
  version "0.8.4"
  dependencies:
    "@antfu/utils" "^0.7.7"
    "@rollup/pluginutils" "^5.1.0"
    debug "^4.3.4"
    error-stack-parser-es "^0.1.1"
    fs-extra "^11.2.0"
    open "^10.1.0"
    perfect-debounce "^1.0.0"
    picocolors "^1.0.0"
    sirv "^2.0.4"

vite-plugin-vue-inspector@^5.1.2:
  version "5.1.2"
  dependencies:
    "@babel/core" "^7.23.0"
    "@babel/plugin-proposal-decorators" "^7.23.0"
    "@babel/plugin-syntax-import-attributes" "^7.22.5"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-transform-typescript" "^7.22.15"
    "@vue/babel-plugin-jsx" "^1.1.5"
    "@vue/compiler-dom" "^3.3.4"
    kolorist "^1.8.0"
    magic-string "^0.30.4"

vite-plugin-windicss@^1.9.3:
  version "1.9.3"
  dependencies:
    "@windicss/plugin-utils" "1.9.3"
    debug "^4.3.4"
    kolorist "^1.8.0"
    windicss "^3.5.6"

vite@^5.0.0, vite@^5.3.2:
  version "5.3.3"
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.39"
    rollup "^4.13.0"
  optionalDependencies:
    fsevents "~2.3.3"

vscode-jsonrpc@6.0.0:
  version "6.0.0"

vscode-languageclient@^7.0.0:
  version "7.0.0"
  dependencies:
    minimatch "^3.0.4"
    semver "^7.3.4"
    vscode-languageserver-protocol "3.16.0"

vscode-languageserver-protocol@3.16.0:
  version "3.16.0"
  dependencies:
    vscode-jsonrpc "6.0.0"
    vscode-languageserver-types "3.16.0"

vscode-languageserver-textdocument@^1.0.1:
  version "1.0.11"

vscode-languageserver-types@3.16.0:
  version "3.16.0"

vscode-languageserver@^7.0.0:
  version "7.0.0"
  dependencies:
    vscode-languageserver-protocol "3.16.0"

vscode-uri@^3.0.2:
  version "3.0.8"

vue-bundle-renderer@^2.1.0:
  version "2.1.0"
  dependencies:
    ufo "^1.5.3"

vue-demi@^0.14.6, vue-demi@^0.14.8, vue-demi@>=0.14.5:
  version "0.14.8"

vue-devtools-stub@^0.1.0:
  version "0.1.0"

vue-easy-lightbox@^1.19.0:
  version "1.19.0"

vue-eslint-parser@^10.1.3:
  version "10.1.3"
  dependencies:
    debug "^4.4.0"
    eslint-scope "^8.2.0"
    eslint-visitor-keys "^4.2.0"
    espree "^10.3.0"
    esquery "^1.6.0"
    lodash "^4.17.21"
    semver "^7.6.3"

vue-eslint-parser@^9.4.2, vue-eslint-parser@^9.4.3:
  version "9.4.3"
  dependencies:
    debug "^4.3.4"
    eslint-scope "^7.1.1"
    eslint-visitor-keys "^3.3.0"
    espree "^9.3.1"
    esquery "^1.4.0"
    lodash "^4.17.21"
    semver "^7.3.6"

vue-i18n@^9.9.0:
  version "9.13.1"
  dependencies:
    "@intlify/core-base" "9.13.1"
    "@intlify/shared" "9.13.1"
    "@vue/devtools-api" "^6.5.0"

vue-router@^4.2.5, vue-router@^4.4.0:
  version "4.4.0"
  dependencies:
    "@vue/devtools-api" "^6.5.1"

vue-slider-component@4.1.0-beta.7:
  version "4.1.0-beta.7"

vue-tel-input@6.0.5:
  version "6.0.5"
  dependencies:
    libphonenumber-js "^1.10.12"

vue@^2.0.0:
  version "2.7.16"
  dependencies:
    "@vue/compiler-sfc" "2.7.16"
    csstype "^3.1.0"

vue@^3.4.31:
  version "3.4.31"
  dependencies:
    "@vue/compiler-dom" "3.4.31"
    "@vue/compiler-sfc" "3.4.31"
    "@vue/runtime-dom" "3.4.31"
    "@vue/server-renderer" "3.4.31"
    "@vue/shared" "3.4.31"

webidl-conversions@^3.0.0:
  version "3.0.1"

webpack-sources@^3.2.3:
  version "3.2.3"

webpack-virtual-modules@^0.5.0:
  version "0.5.0"

webpack-virtual-modules@^0.6.1:
  version "0.6.2"

whatwg-url@^5.0.0:
  version "5.0.0"
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

which@^2.0.1:
  version "2.0.2"
  dependencies:
    isexe "^2.0.0"

which@^3.0.1:
  version "3.0.1"
  dependencies:
    isexe "^2.0.0"

wide-align@^1.1.2:
  version "1.1.5"
  dependencies:
    string-width "^1.0.2 || 2 || 3 || 4"

windicss-analysis@^0.3.5:
  version "0.3.5"
  dependencies:
    "@windicss/plugin-utils" "^1.1.1"
    cac "^6.7.3"
    connect "^3.7.0"
    declass "^0.0.1"
    fast-glob "^3.2.6"
    fs-extra "^10.0.0"
    sirv "^1.0.12"

windicss-webpack-plugin@^1.8.0:
  version "1.8.0"
  dependencies:
    "@windicss/plugin-utils" "^1.9.1"
    debug "^4.3.4"
    get-port "^6.1.2"
    loader-utils "^2.0.0"
    lodash "^4.17.21"
    pathe "^1.1.0"
    webpack-virtual-modules "^0.5.0"
    windicss "^3.5.6"

windicss@^3.5.6:
  version "3.5.6"

winston-discord-transport@^1.3.0:
  version "1.3.0"
  dependencies:
    logform "2.1.2"
    superagent "5.2.2"
    winston-transport "4.3.0"

winston-graylog2@^2.1.2:
  version "2.1.2"
  dependencies:
    graylog2 "^0.2.1"
    winston "^3.2.0"
    winston-transport "^4.3.0"

winston-transport@^4.3.0, winston-transport@^4.7.0:
  version "4.7.1"
  dependencies:
    logform "^2.6.1"
    readable-stream "^3.6.2"
    triple-beam "^1.3.0"

winston-transport@4.3.0:
  version "4.3.0"
  dependencies:
    readable-stream "^2.3.6"
    triple-beam "^1.2.0"

winston@^3.13.1, winston@^3.2.0:
  version "3.13.1"
  dependencies:
    "@colors/colors" "^1.6.0"
    "@dabh/diagnostics" "^2.0.2"
    async "^3.2.3"
    is-stream "^2.0.0"
    logform "^2.6.0"
    one-time "^1.0.0"
    readable-stream "^3.4.0"
    safe-stable-stringify "^2.3.1"
    stack-trace "0.0.x"
    triple-beam "^1.3.0"
    winston-transport "^4.7.0"

word-wrap@^1.2.5:
  version "1.2.5"

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrap-ansi@^9.0.0:
  version "9.0.0"
  dependencies:
    ansi-styles "^6.2.1"
    string-width "^7.0.0"
    strip-ansi "^7.1.0"

wrappy@1:
  version "1.0.2"

ws@^8.17.1:
  version "8.18.0"

xml-name-validator@^4.0.0:
  version "4.0.0"

y18n@^5.0.5:
  version "5.0.8"

yallist@^3.0.2:
  version "3.1.1"

yallist@^4.0.0:
  version "4.0.0"

yaml-eslint-parser@^1.2.1, yaml-eslint-parser@^1.3.0:
  version "1.3.0"
  dependencies:
    eslint-visitor-keys "^3.0.0"
    yaml "^2.0.0"

yaml-eslint-parser@^1.2.2:
  version "1.2.3"
  dependencies:
    eslint-visitor-keys "^3.0.0"
    lodash "^4.17.21"
    yaml "^2.0.0"

yaml@^2.0.0, yaml@^2.4.5, yaml@~2.4.2:
  version "2.4.5"

yaml@^2.7.0:
  version "2.7.1"

yargs-parser@^21.1.1:
  version "21.1.1"

yargs@^17.0.0, yargs@^17.5.1:
  version "17.7.2"
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yocto-queue@^0.1.0:
  version "0.1.0"

yocto-queue@^1.0.0:
  version "1.1.1"

zhead@^2.2.4:
  version "2.2.4"

zip-stream@^6.0.1:
  version "6.0.1"
  dependencies:
    archiver-utils "^5.0.0"
    compress-commons "^6.0.2"
    readable-stream "^4.0.0"

zod-to-json-schema@^3.24.1:
  version "3.24.5"

zod@^3.23.8, zod@^3.24.2:
  version "3.24.4"

zwitch@^2.0.0:
  version "2.0.4"
