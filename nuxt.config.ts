// https://v3.nuxtjs.org/docs/directory-structure/nuxt.config
export default defineNuxtConfig({
  hooks: {
    'build:manifest': (manifest) => {
      for (const key in manifest) {
        // or other logic
        manifest[key].dynamicImports = []
        manifest[key].prefetch = false
        manifest[key].preload = false
      }
    }
  },

  app: {
    head: {
      charset: 'utf-8',
      viewport: 'initial-scale=1.0, user-scalable=no, maximum-scale=1, width=device-width',
      meta: [{
        name: 'HandheldFriendly',
        content: 'True'
      }, {
        name: 'MobileOptimized',
        content: '320'
      }],
      link: [{
        rel: 'preconnect',
        href: 'https://fonts.gstatic.com'
      }, {
        rel: 'preconnect',
        href: process.env.NUXT_PUBLIC_BASE_IMAGE_URL || 'https://img.cloudimgs.net'
      }, {
        rel: 'dns-prefetch',
        href: process.env.NUXT_PUBLIC_BASE_IMAGE_URL || 'https://img.cloudimgs.net'
      }]
    }
  },

  css: [
    '~/assets/sass/app.scss',
    'virtual:windi.css'
  ],

  experimental: {
    asyncContext: true,
    renderJsonPayloads: false
  },

  modules: [
    '@pinia/nuxt',
    '@pinia-plugin-persistedstate/nuxt',
    'nuxt-windicss',
    '@nuxtjs/i18n',
    'nuxt-viewport',
    'nuxt-schema-org',
    '@nuxt/eslint',
  ],

  eslint: {
    // checker: true,
  },

  schemaOrg: {
    minify: true,
  },

  i18n: {
    vueI18n: './i18n.config.ts',
    lazy: true,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root', // recommended
      alwaysRedirect: true,
    },
    locales: [
      {
        name: 'English (US)',
        countryCode: ['US', 'EN'],
        code: 'en',
        date: 'en-US',
        iso: 'en',
        file: 'en.json'
      },
      {
        name: 'English (UK)',
        countryCode: ['GB', 'GBR'],
        code: 'en-gb',
        date: 'en-GB',
        iso: 'en-GB',
        file: 'en-GB.json'
      },
      {
        name: 'اللغة العربية',
        countryCode: ['PS', 'PSE', 'DZ', 'DZA', 'BH', 'BHR', 'TD', 'TCD', 'KM', 'COM', 'DJ', 'DJI', 'EG', 'EGY', 'IQ', 'IRQ', 'JO', 'JOR', 'KW', 'KWT', 'LB', 'LBN', 'LY', 'LBY', 'MR', 'MRT', 'MA', 'MAR', 'OM', 'OMN', 'QA', 'QAT', 'SA', 'SAU', 'SO', 'SOM', 'SD', 'SDN', 'SY', 'SYR', 'TN', 'TUN', 'AE', 'ARE', 'YE', 'YEM'],
        code: 'ar',
        iso: 'ar',
        date: 'ar-EG',
        file: 'ar.json',
        dir: 'rtl'
      },
      {
        name: 'বাংলা ভাষা',
        countryCode: ['BD', 'BGD'],
        code: 'bn',
        iso: 'bn',
        file: 'bn.json'
      },
      {
        name: 'Česky',
        countryCode: ['CZ', 'CZE'],
        code: 'cs',
        iso: 'cs',
        date: 'cs',
        file: 'cs.json'
      },
      {
        name: 'Dansk',
        countryCode: ['DK', 'DNK'],
        code: 'da',
        iso: 'da',
        date: 'da-DK',
        file: 'da.json'
      },
      {
        name: 'Deutsch',
        countryCode: ['DE', 'DEU', 'AT', 'AUT', 'BE', 'BEL', 'LU', 'LUX', 'CH', 'CHE', 'LI', 'LIE'],
        code: 'de',
        iso: 'de',
        date: 'de-DE',
        file: 'de.json'
      },
      {
        name: 'Ελληνική Γλώσσα',
        countryCode: ['GR', 'GRC'],
        code: 'el',
        iso: 'el',
        date: 'el',
        file: 'el.json'
      },
      {
        name: 'Español',
        countryCode: ['ES', 'ESP', 'AR', 'ARG', 'BO', 'BOL', 'CL', 'CHL', 'CO', 'COL', 'CR', 'CRI', 'CU', 'CUB', 'DO', 'DOM', 'EC', 'ECU', 'SV', 'SLV', 'GT', 'GTM', 'HN', 'HND', 'MX', 'MEX', 'NI', 'NIC', 'PA', 'PAN', 'PY', 'PRY', 'PE', 'PER', 'UY', 'URY', 'VE', 'VEN'],
        code: 'es',
        iso: 'es',
        date: 'es',
        file: 'es.json'
      },
      {
        name: 'Eesti Keel',
        countryCode: ['EE', 'EST'],
        code: 'et',
        iso: 'et',
        date: 'et-EE',
        file: 'et.json'
      },
      {
        name: 'Suomen Kieli',
        countryCode: ['SO', 'SOM'],
        code: 'fi',
        iso: 'fi',
        date: 'fi-FI',
        file: 'fi.json'
      },
      {
        name: 'Français',
        countryCode: ['FR', 'FRA', 'GA', 'GAB', 'GN', 'GIN', 'ML', 'MLI', 'MC', 'MCO', 'NE', 'NER', 'SN', 'SEN', 'TG', 'TGO', 'CA', 'CAN'],
        code: 'fr',
        iso: 'fr',
        date: 'fr-FR',
        file: 'fr.json'
      },
      {
        name: 'Hrvatski',
        countryCode: ['HR', 'HRV'],
        code: 'hr',
        iso: 'hr',
        date: 'hr',
        file: 'hr.json'
      },
      {
        name: 'Magyar Nyelv',
        countryCode: ['HU', 'HUN'],
        code: 'hu',
        iso: 'hu',
        date: 'hu-HU',
        file: 'hu.json'
      },
      {
        name: 'Bahasa Indonesia',
        countryCode: ['ID', 'IDN'],
        code: 'id',
        iso: 'id',
        file: 'id.json'
      },
      {
        name: 'Italiano',
        countryCode: ['IT', 'ITA'],
        code: 'it',
        iso: 'it',
        date: 'it-IT',
        file: 'it.json'
      },
      {
        name: '日本語',
        countryCode: ['JP', 'JPN'],
        code: 'ja',
        iso: 'ja',
        date: 'ja-JP',
        file: 'ja.json'
      },
      {
        name: '한국어',
        countryCode: ['KR', 'KOR'],
        code: 'ko',
        iso: 'ko',
        date: 'ko-KR',
        file: 'ko.json'
      },
      {
        name: 'Lietuvių kalba',
        countryCode: ['LT', 'LTU'],
        code: 'lt',
        iso: 'lt',
        file: 'lt.json'
      },
      {
        name: 'Bahasa Melayu',
        countryCode: ['MY', 'MYS'],
        code: 'ms',
        iso: 'ms',
        file: 'ms.json'
      },
      {
        name: 'Nederlands',
        countryCode: ['NL', 'NLD'],
        code: 'nl',
        iso: 'nl',
        date: 'nl-NL',
        file: 'nl.json'
      },
      {
        name: 'Norsk',
        countryCode: ['NO', 'NOR'],
        code: 'no',
        iso: 'no',
        date: 'no-NO',
        file: 'no.json'
      },
      {
        name: 'Polski',
        countryCode: ['PL', 'POL'],
        code: 'pl',
        iso: 'pl',
        date: 'pl-PL',
        file: 'pl.json'
      },
      {
        name: 'Português',
        countryCode: ['PT', 'PRT', 'BR', 'BRA', 'CV', 'CPV', 'AO', 'AGO', 'MZ', 'MOZ', 'GW', 'GNB', 'ST', 'STP'],
        code: 'pt',
        iso: 'pt',
        date: 'pt-PT',
        file: 'pt.json'
      },
      {
        name: 'Limba Română',
        countryCode: ['RO', 'ROU'],
        code: 'ro',
        iso: 'ro',
        date: 'ro-RO',
        file: 'ro.json'
      },
      {
        name: 'Slovenský jazyk',
        countryCode: ['SK', 'SVK'],
        code: 'sk',
        iso: 'sk',
        file: 'sk.json'
      },
      {
        name: 'Slovenski Jezik',
        countryCode: ['SI', 'SVN'],
        code: 'sl',
        iso: 'sl',
        date: 'sl-SI',
        file: 'sl.json'
      },
      {
        name: 'Gjuha shqipe',
        countryCode: ['AL', 'ALB'],
        code: 'sq',
        iso: 'sq',
        file: 'sq.json'
      },
      {
        name: 'Svenska',
        countryCode: ['SE', 'SWE'],
        code: 'sv',
        iso: 'sv',
        date: 'sv-SE',
        file: 'sv.json'
      },
      {
        name: 'Türkçe',
        countryCode: ['TR', 'TUR', 'CY', 'CYP'],
        code: 'tr',
        iso: 'tr',
        date: 'tr-TR',
        file: 'tr.json'
      },
      {
        name: 'Tiếng Việt',
        countryCode: ['VN', 'VNM'],
        code: 'vi',
        iso: 'vi',
        date: 'vi',
        file: 'vi.json'
      },
      {
        name: '中文',
        countryCode: ['CN', 'CHN', 'TW', 'TWN', 'HK', 'HKG'],
        code: 'zh',
        iso: 'zh',
        date: 'zh-CN',
        file: 'zh.json'
      }
    ],
    langDir: 'locales',
    defaultLocale: 'en',
    debug: false
  },

  runtimeConfig: {
    public: {
      version: '4.0.0.0001',
      appEnv: process.env.NUXT_PUBLIC_APP_ENV,
      runType: process.env.NUXT_PUBLIC_RUN_TYPE,
      xDomain: process.env.NUXT_PUBLIC_X_DOMAIN,
      baseUrl: process.env.NUXT_PUBLIC_BASE_URL,

      trackingUrl: process.env.NUXT_PUBLIC_TRACKING_URL,
      newTrackingUrl: process.env.NUXT_PUBLIC_NEW_TRACKING_URL,
      traceUrl: process.env.NUXT_PUBLIC_TRACE_URL,

      googleMapsApiKey: process.env.NUXT_PUBLIC_GOOGLE_MAPS_API_KEY,
      baseImageUrl: process.env.NUXT_PUBLIC_BASE_IMAGE_URL,
      cdnUrl: process.env.NUXT_PUBLIC_CDN_URL || 'https://cdn.cloudimgs.net',

      aswStorageClass: process.env.NUXT_PUBLIC_AWS_STORAGE_CLASS || 'INTELLIGENT_TIERING',

      personalBridgeUrl: process.env.NUXT_PUBLIC_PERSONAL_BRIDGE_URL,
      recaptchaSiteKey: process.env.RECAPTCHA_SITE_KEY || '6LdO3NwqAAAAAGHNjYSPjC_IoMfbE7nYez89mzg9',

      sentryDSN: process.env.NUXT_PUBLIC_SENTRY_DSN,
      sentrySampleRate: process.env.NUXT_PUBLIC_SENTRY_SAMPLE_RATE,
    },

    proxyApiUrl: process.env.NUXT_PROXY_API_URL,
    proxyWriteApiUrl: process.env.NUXT_PROXY_WRITE_API_URL,
    discordWebhook: process.env.NUXT_DISCORD_WEBHOOK || '',
    loggerSampleRate: process.env.LOGGER_SAMPLE_RATE || '0',
  },

  nitro: {
    compressPublicAssets: true,
    minify: true,
  },

  build: {
    transpile: ['tslib']
  },

  compatibilityDate: '2024-07-31'
})
